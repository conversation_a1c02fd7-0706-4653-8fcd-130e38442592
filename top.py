import csv

def main():
    input_csv_file = 'redis_all_keys.csv'
    output_csv_file = 'redis_top_level_prefixes_sorted.csv'
    
    top_level_prefixes = set()

    try:
        # 1. 读取输入CSV文件
        with open(input_csv_file, 'r', newline='', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            header = next(reader, None)  # 尝试读取表头
            
            # 处理表头
            if header:
                if header[0].lower().strip() != 'key':
                    # 如果表头不是"key"，尝试提取前缀
                    key_value = header[0].strip()
                    if ':' in key_value:
                        prefix = key_value.split(':', 1)[0]
                        if prefix:  # 确保前缀非空
                            top_level_prefixes.add(prefix)
            
            # 处理数据行
            for row in reader:
                if row:
                    key_value = row[0].strip()
                    if key_value and ':' in key_value:
                        prefix = key_value.split(':', 1)[0]
                        if prefix:  # 确保前缀非空
                            top_level_prefixes.add(prefix)
        
        if not top_level_prefixes:
            print(f"在 '{input_csv_file}' 文件中没有找到有效的顶级前缀。将创建一个空的输出文件。")
            with open(output_csv_file, 'w', newline='', encoding='utf-8') as outfile:
                pass 
            return
        
        print(f"从 '{input_csv_file}' 文件中提取到 {len(top_level_prefixes)} 个唯一的顶级前缀。")
        
        # 2. 排序前缀
        sorted_prefixes = sorted(top_level_prefixes)
        print("顶级前缀已按字典序完成排序。")
        
        # 3. 写入输出文件
        with open(output_csv_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            writer.writerow(["Top-Level Prefix"])  # 写入表头
            for prefix in sorted_prefixes:
                writer.writerow([prefix])
        
        print(f"处理完成。结果已保存到 '{output_csv_file}'。")
        
    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_csv_file}' 未找到。")
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == "__main__":
    main()