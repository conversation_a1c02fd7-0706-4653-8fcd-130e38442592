redis_key_usage_report.md# Redis Key Usage Report

## Key: `app`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**
The prefix `app:` or `app_` is a generic prefix used for various application-level data. It's often combined with more specific identifiers.

* **Market Information:**
  * `app_market:<market_id>`: Stores details for a specific market.
  * `app_market_list`: A list of markets.
  * Used in conjunction with `all_market_list`, `online_market_list`, `online_market_list_dev`.
* **Coin Information:**
  * `app_coin:<coin_key>`: Stores details for a specific coin (older configuration).
  * `app:<coin_key>`: Stores details for a specific coin or trading pair (newer configuration, especially in `conf` Redis database).
  * `app_coin_list`: A list of coins.
  * Used with `all_coin_list`, `online_coin_list`, `online_coin_list_dev`.
* **Application Adjustment/Synchronization:**
  * `app_adjust_time`: Timestamp for the last adjustment in the production environment.
  * `app_adjust_time_dev`: Timestamp for the last adjustment in the development environment.
  * `app_adjust_v1_list`: Sorted set logging changes for production data synchronization.
  * `app_adjust_v1_list_dev`: Sorted set logging changes for development data synchronization.
* **Tab Configuration:**
  * `app_mix_tab_coins:<tab_key>`: Sorted set storing coins within a specific 'mix' tab.
  * `app_tab_coins:<tab_key>`: Sorted set storing coins within a specific tab (older version).
  * `app_mix_tab_list`, `app_tab_list`: Lists of tabs.
  * `app_mix_tab:<tab_key>`, `app_tab:<tab_key>`: Hash storing details of a specific tab.
* **Trading Pair Information:**
  * `app:<trading_pair_key>`: Hash storing cached data for a trading pair.
  * `app_market_coins:<market_key>`: Sorted set storing trading pairs for a given market.
* **User Data (users Redis database):**
  * `user_default_group:<user_id>`: Stores the default custom group for a user.
  * `user_custom_group:<user_id>`: Sorted set storing custom groups for a user.
  * `custom_group_key:<group_id>`: Sorted set storing trading pairs within a user's custom group.

**Files Found:**

* `app/Traits/MarketTrait.php`
* `app/Http/Controllers/Monitor/ApiStateController.php`
* `app/Http/Dao/Redis/Coin/CoinConfDao.php`
* `app/Traits/MarketCoinIndexTrait.php`
* `app/Traits/TabTrait.php`
* `app/Traits/TradingPairTrait.php`
* `app/Console/Commands/AttributeAdd.php`
* `app/Http/Controllers/UserCustomController.php`
* `app/Http/Controllers/FeedBack/CoinApplyController.php`
* ... (many other files reference keys starting with `app:` or `app_`)

## Key: `app_coin_offline_markets`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* The key is structured as `app_coin_offline_markets{$dev}:{$coin_type}`.
* It is a Redis Sorted Set.
* It stores markets that are offline for a specific `{$coin_type}`.
* `{$dev}` likely indicates an environment suffix (e.g., `_dev` for development).
* When a trading pair is brought online, the corresponding market is removed from this set. This helps in managing lists of markets that are not currently active for a given coin type.

**Files Found:**

* `app/Traits/TradingPairTrait.php` (specifically in `onlineMarketCoinConf` and related methods)

## Key: `app_coin_offline_markets_dev`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* The key is structured as `app_coin_offline_markets_dev:<coin_type>`.
* This is the development environment-specific version of `app_coin_offline_markets:<coin_type>`.
* It is a Redis Sorted Set.
* It stores markets that are offline for a specific `<coin_type>` in the development environment.
* When a trading pair is brought online in the development environment, the corresponding market is removed from this set.

**Files Found:**

* `app/Traits/TradingPairTrait.php` (logic often uses a `$dev` suffix variable, which would be `_dev` in this case)

---

## Key Prefix: `app_coin_tab` (and related `app_*_tab`)

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern:** `app_<class_key>_tab:<tab_key>` (e.g., `app_coin_tab:<tab_key>`, `app_futures_tab:<tab_key>`, `app_market_tab:<tab_key>`, `app_mix_tab:<tab_key>`, `app_sidebar_tab:<tab_key>`) and older `app_tab:<tab_key>`.

**Usage:**

* These keys typically store metadata for specific application tabs using **Redis Hashes**.
* The `<class_key>` differentiates tab categories (e.g., `mix`, `futures`, `coin`).
* The `<tab_key>` is the unique identifier for the tab.
* Attributes stored can include tab name, display properties, type, and online status.
* Logic is primarily managed in `app/Traits/TabTrait.php`.
* Uses both `master` and `conf` Redis connections depending on the tab type and age of the configuration.

**Files Found:**

* `app/Traits/TabTrait.php`
* `app/Http/Controllers/TabController.php`

---

## Key Prefix: `app_coin_tab_coins` (and related `app_*_tab_coins`)

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern:** `app_<class_key>_tab_coins:<tab_key>` (e.g., `app_coin_tab_coins:<tab_key>`, `app_futures_tab_coins:<tab_key>`, `app_market_tab_coins:<tab_key>`, `app_mix_tab_coins:<tab_key>`).

**Usage:**

* These keys are **Redis Sorted Sets**.
* They store the identifiers of items (e.g., coins, trading pairs, or other content) belonging to a specific tab, identified by `<class_key>` and `<tab_key>`.
* The score in the sorted set is often used for ordering these items within the tab.
* Logic is primarily managed in `app/Traits/TabTrait.php`.
* Primarily uses the `master` Redis connection.

**Files Found:**

* `app/Traits/TabTrait.php`
* `app/Console/Commands/TradingBatchOff.php`

---

## Key Prefix: `app_coin_tab_coins_online`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern:** `app_<class_key>_tab_coins_online:<tab_key>` (e.g., `app_coin_tab_coins_online:<tab_key>`, `app_futures_tab_coins_online:<tab_key>`, `app_market_tab_coins_online:<tab_key>`, `app_mix_tab_coins_online:<tab_key>`).

**Usage:**

* This is a **Redis Sorted Set**.
* It stores items that are currently online within a specific tab for the **production environment**.
* The `<class_key>` and `<tab_key>` define the specific tab.
* Managed by `app/Traits/TabTrait.php`.
* Uses the `conf` Redis connection.

**Files Found:**

* `app/Traits/TabTrait.php`
* `app/Http/Controllers/TabController.php`

---

## Key Prefix: `app_coin_tab_coins_online_dev`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern:** `app_<class_key>_tab_coins_online_dev:<tab_key>` (e.g., `app_coin_tab_coins_online_dev:<tab_key>`, `app_futures_tab_coins_online_dev:<tab_key>`, `app_market_tab_coins_online_dev:<tab_key>`, `app_mix_tab_coins_online_dev:<tab_key>`).

**Usage:**

* This is a **Redis Sorted Set**.
* It stores items that are currently online within a specific tab for the **development environment**.
* The `<class_key>` and `<tab_key>` define the specific tab.
* The `_dev` suffix indicates the development environment.
* Managed by `app/Traits/TabTrait.php`.
* Uses the `conf` Redis connection.

**Files Found:**

* `app/Traits/TabTrait.php`
* `app/Http/Controllers/TabController.php`

---

## Key Prefix: `app_futures_tab`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern & Usage:**
This prefix is part of the application's tab management system, specifically for tabs related to "futures" content. It follows similar patterns to other `app_*_tab` keys (like `app_coin_tab`, `app_mix_tab`).

* **Tab Metadata (Redis Hash):**
  * `app_futures_tab:<tab_key>`: Stores metadata for an individual tab within the "futures" category (e.g., tab name, display properties, type, online status).
* **Tab Content (Redis Sorted Set):**
  * `app_futures_tab_coins:<tab_key>`: Stores identifiers of items (likely trading pairs or related content) belonging to a specific futures tab, ordered by score.
  * `app_futures_tab_coins_online:<tab_key>`: Stores items online in a specific futures tab for the production environment.
  * `app_futures_tab_coins_online_dev:<tab_key>`: Stores items online in a specific futures tab for the development environment.
* **Hot Tab Content (Redis Sorted Set):** (Derived from `app_futures_tab_hot_online` and `app_futures_tab_hot_online_dev` found in `redis_key_prefix_analysis.txt`)
  * `app_futures_tab_hot_online:<tab_key>`: Stores "hot" or specially featured items online in a specific futures tab for production.
  * `app_futures_tab_hot_online_dev:<tab_key>`: Stores "hot" or specially featured items online in a specific futures tab for development.
* **Tab List (Redis Sorted Set - Potential):**
  * While `app_futures_tab_list` is not explicitly in the `redis_top_level_prefixes_sorted.csv`, a pattern like `app_<class_key>_tab_list` (e.g., `app_mix_tab_list`) exists for other tab types to list all tabs of that class.

**Primary Logic Location:**

* The logic for managing these tabs (creation, updates, content management, online/offline status) is primarily handled within `app/Traits/TabTrait.php` and exposed through `app/Http/Controllers/TabController.php`.

**Files Found (General Tab System):**

* `app/Traits/TabTrait.php`
* `app/Http/Controllers/TabController.php`
* Relevant API routes in `routes/api.php`.
* Frontend API consumption in `frontend/src/api/tab.js` and `frontend/src/api/api.js`.

---

## Key Prefix: `app_index_coins`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `app_index_coins` itself is a namespace. The actual data is stored in keys following the pattern `app_index_coins:i:<index_key>`.
* The `<index_key>` is the identifier of a specific index (e.g., `blc10`, `csi100`, as seen in `redis_key_prefix_paths_sorted.csv`).
* These specific keys (e.g., `app_index_coins:i:blc10`) are likely **Redis Sorted Sets**.
* The members of the sorted set are coin identifiers (e.g., `btc`, `eth`).
* The score associated with each coin member could represent its weight or ranking within that index.
* This allows the application to retrieve all coins belonging to a specific index and their order/weight.

**Related Keys:**

* `app_index_list`: A sorted set in the `conf` Redis DB, listing available indices.
* `app_index_component:*`: Stores components or metadata about components of an index (e.g., `app_index_component:i:<index_key>:<component_name>`).
* `app_coin_list`: A list of all coins.

**Primary Logic Location (for index management in general):**

* `app/Traits/MarketCoinIndexTrait.php`
* `app/Http/Controllers/IndexManageController.php` (uses `MarketCoinIndexTrait`)

---

## Key Prefix: `app_index_component`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `app_index_component` is a namespace for storing details about the components of various indices.
* The actual data is stored in keys following the pattern `app_index_component:i:<index_key>` or `app_index_component:i:<index_key>:<component_name>`.
  * `<index_key>`: The identifier of a specific index (e.g., `blc10`, `csi100`).
  * `<component_name>`: The name of a specific component within that index (e.g., `bitmain`, `chainext`).
* These keys are likely **Redis Hashes**.
* They store attributes and metadata about each component of an index. For example, for an index `csi100` and its component `chainext`, the key `app_index_component:i:csi100:chainext` might store information about `chainext`'s role or data related to the `csi100` index.

**Primary Logic Location:**

* Similar to `app_index_coins`, the primary logic is handled within `app/Traits/MarketCoinIndexTrait.php` and `app/Http/Controllers/IndexManageController.php`.

**Files Found (Related to index management):**

* `app/Traits/MarketCoinIndexTrait.php`
* `app/Http/Controllers/IndexManageController.php`

---

## Key Prefix: `app_market`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**
This prefix is central to managing market (exchange) information within the application.

* **Market Details (Redis Hash):**
  * `app_market:<market_id>`: Stores detailed information for a specific market, such as its name, ID, and other attributes. This pattern is used in both `master` and `conf` Redis connections.
* **Market Lists (Redis Sorted Set):**
  * `app_market_list`: Located in the `master` Redis connection, this sorted set stores a list of market keys (`<market_id>`), often ordered by a rank or timestamp.
  * Related lists like `all_market_list`, `online_market_list`, and `online_market_list_dev` exist in the `conf` Redis connection, categorizing markets by their overall existence and online status (production/development).
* **Market Coins (Redis Sorted Set):**
  * `app_market_coins:<market_key>`: Located in the `master` Redis connection, this sorted set stores the trading pairs or coins associated with a specific market.

**Primary Logic Location & Files:**

* `app/Traits/MarketTrait.php`: Core logic for CRUD operations on market data and managing related Redis caches.
* `app/Traits/MarketApplyTrait.php`: Handles market application and approval processes, interacting with these cache keys.
* `app/Console/Commands/AttributeAdd.php`: Updates `app_market:` caches when market attributes are modified.
* `app/Console/Commands/TradingBatchOff.php`: Interacts with `app_market:<market_id>` and online status lists during batch on/off-boarding of trading pairs/markets.
* `app/Http/Controllers/Monitor/ApiStateController.php`: Fetches market names using `app_market:<market_id>`.
* `app/Traits/AnalysisTrait.php`: Uses `app_market_list` and `app_market_coins` for analytical purposes.
* `app/Http/Controllers/MarketController.php`: Retrieves market lists for display or API responses.

---

## Key Prefix: `app_sidebar_index`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**
This prefix is used to manage indices displayed in the application's sidebar.

* **Sidebar Index List (Redis Sorted Set/List):**
  * `app_sidebar_index_list`: Stores a list of keys for the indices that appear in the sidebar.
* **Sidebar Index Detail (Redis Hash):**
  * `app_sidebar_index:<key>`: Stores detailed information for an individual sidebar index item, such as its name, type, and online status.

**Primary Logic Location & Files:**

* `app/Console/Commands/AppSidebarAdd.php`: This command is responsible for adding predefined indices (e.g., "BTC Index", "Total Market Cap") to the sidebar and managing their corresponding Redis entries under `app_sidebar_index_list` and `app_sidebar_index:<key>`.

---

## Key Prefix: `app_test`

**Project:** General, specific usage in `/Users/<USER>/back.aicoin.com` is not prominent in core application code.

**Usage:**

* The key `app_test` is listed in `redis_top_level_prefixes_sorted.csv`.
* The file `redis_key_prefix_paths.csv` shows potential specific keys like `app_test/app_test` or `app_test/test`, which could translate to Redis keys `app_test` or `app_test:test`.
* Semantic searches within the `/Users/<USER>/back.aicoin.com` project did not reveal significant, direct programmatic usage of a top-level key named `app_test` within the main application logic.
* This suggests that `app_test` might be:
  * Used for ad-hoc or manual testing purposes.
  * A legacy or deprecated key.
  * Utilized in scripts or auxiliary tools not covered by the primary backend code search.
* Without more specific code references, its exact programmatic role in the main application remains unclear, though its presence in the key lists is noted.

---

## Key Prefix: `cache_okxord_token`

**Project:** `/Users/<USER>/kline-data-scrapy` (primarily)

**Usage:**

* The prefix `cache_okxord_token:` is used as a namespace for caching data related to Ordinals tokens (likely BRC-20 or similar) from the OKX exchange.
* Specific keys follow the pattern `cache_okxord_token:<token_identifier>`, where `<token_identifier>` is the symbol or ID of the token (e.g., `$$ethbtc`, `$bmpbtc`, `satsbtc`).
* These keys likely store **Hashes** or **Strings** containing cached information about each token, such as price, volume, or other metadata fetched from the OKX API.
* The primary purpose is to cache this data to reduce direct API calls to OKX and improve performance when accessing token information.

**Primary Logic Location & Files:**

* The script responsible for fetching and caching this data appears to be located within the `/Users/<USER>/kline-data-scrapy` project.
* A likely candidate file, based on naming conventions and typical project structure for scrapers, would be something like `apps/Scrapy/OkxOrdTokens.php` or a similar script that interacts with the OKX API for Ordinals/BRC-20 tokens. (This is an inference as the exact file was not in the direct search results for the *prefix* but is suggested by the specific key names found in `redis_key_prefix_paths_sorted.csv`).

---

## Key Prefix: `coin:`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* The prefix `coin:` is used as a namespace for storing detailed information and configurations related to individual cryptocurrencies (coins).
* Specific keys typically follow the pattern `coin:<coin_key>`, where `<coin_key>` is the unique identifier for the coin (e.g., `btc`, `eth`).
* These keys primarily store **Redis Hashes** containing various attributes of a coin, such as its name, symbol, logo, description, associated market data, and other configuration details.
* This prefix is often seen in conjunction with `app_coin:<coin_key>` or `app:<coin_key>` where `app_coin:` might represent an older or specific subset of coin data, while `app:<coin_key>` (especially in the `conf` Redis database) is used for broader coin/trading pair configurations.

**Primary Logic Location & Files:**

* `app/Traits/CoinTrait.php`: Contains core logic for fetching and managing coin data, including interactions with Redis keys like `app_coin_list` and `app_coin_markets:<coin>`.
* `app/Traits/CoinApplyTrait.php`: Handles processes related to coin applications and updates, which involves reading from and writing to coin-related Redis caches (e.g., `app_coin:<coin_key>`, `app_currency:<coin_key>:<market_key>`).
* `app/Http/Dao/Redis/Coin/CoinConfDao.php`: Specifically used for fetching coin configuration data from Redis, often using the `app:<coin_key>` pattern in the `conf` database.
* `app/Http/Controllers/FeedBack/CoinApplyController.php`: Interacts with coin data in Redis when processing coin applications or updates. For example, it updates `app_coin:<coin_key>` and `app:<coin_key>` hashes.
* `app/Traits/MarketCoinIndexTrait.php`: Manages market and coin indices. When a coin is deleted, it includes logic to clear associated caches, such as `app_coin:<coin_key>` from the `master` Redis and `app:<coin_key>` from the `conf` Redis.
* `app/Traits/TradingPairTrait.php`: When logging changes related to trading pairs (which inherently involve coins), it might use patterns like `coin:<tradingKey>:<operate>` in adjustment logs stored in Redis (e.g., `app_adjust_v1_list`).

---

## Key Prefix: `coin_chainids`

**Project:** 未在 `/Users/<USER>/back.aicoin.com` PHP 代码中直接发现。可能由其他项目、脚本管理，或为手动维护的键。

**Usage (Inferred):**

* The prefix `coin_chainids` likely stores mappings between cryptocurrency identifiers (e.g., `btc`, `eth`) and their corresponding blockchain chain IDs.
* This is crucial for assets that exist on multiple chains or require specific chain identification for transactions or smart contract interactions.
* **Possible Data Structures:**
  * Could be a single **Hash** named `coin_chainids` where fields are coin symbols/keys and values are their chain ID(s) (perhaps a comma-separated string if multiple).
  * Alternatively, it could be a pattern like `coin_chainids:<coin_key>` storing a **String** or **Set** of chain IDs for that specific coin.
* The absence of direct usage in the searched PHP codebase suggests it might be populated/consumed by other services, scripts, or potentially be a legacy key.

---

## Key Prefix: `coin_currency`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* The prefix `coin_currency:` is used to manage the **pricing currencies (also known as quote currencies or partitions)** associated with a specific base cryptocurrency.
* Keys typically follow the pattern `coin_currency:<coin_key>` or `coin_currency:<trade_type>:<coin_key>`.
  * `<coin_key>`: The identifier of the base cryptocurrency (e.g., `btc`, `eth`).
  * `<trade_type>`: Optional, might specify the type of trading (e.g., `spot`, `futures`).
* These keys are primarily **Redis Sorted Sets**.
* The members of the sorted set are the symbols of the pricing currencies (e.g., `usdt`, `usd`, `btc`) available for the given `<coin_key>`.
* The score associated with each pricing currency member is often used for ordering or ranking them (e.g., display order).
* This structure helps in quickly retrieving all available pricing currencies for a specific coin.

**Primary Logic Location & Files:**

* `app/Traits/TradingPairTrait.php`: This trait contains significant logic for managing trading pairs. When trading pairs are added, modified (e.g., online/offline status changes), or deleted, this trait updates the corresponding `coin_currency:` sorted sets. For example:
  * When a new trading pair `btc_usdt` is made online, `usdt` would be added to the `coin_currency:btc` sorted set.
  * If all trading pairs for `btc` against `usdt` are removed or taken offline, `usdt` would be removed from `coin_currency:btc`.
* `app/Console/Commands/TradingBatchOff.php`: This command, which likely uses `TradingPairTrait.php`, also interacts with `coin_currency:` keys when performing batch offline operations for trading pairs. It ensures that if a pricing currency no longer has any active trading pairs for a given coin, it's removed from the coin's currency list.

**Related Keys:**

* `coin_trading:<coin_key>` or `coin_trading:<trade_type>:<coin_key>`: Stores trading pairs for a coin.
* `coin_trading:<coin_key>:<currency_str>` or `coin_trading:<trade_type>:<coin_key>:<currency_str>`: Stores trading pairs for a coin against a specific pricing currency.

---

## Key Prefix: `coin_currency_offline_hash`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern:**

* `coin_currency_offline_hash:<coin_key>`
* `coin_currency_offline_hash:<coin_key>:<currency_str>`
* `coin_currency_offline_hash_dev:<coin_key>` (for development environment)
* `coin_currency_offline_hash_dev:<coin_key>:<currency_str>` (for development environment)

**Usage:**

* This prefix is used to store information about **trading pairs that are currently offline** for a specific base cryptocurrency (`<coin_key>`) and potentially a specific pricing currency (`<currency_str>`).
* It functions as a **Redis Hash**.
* The fields within the hash are likely the `tradingKey` (identifier of the trading pair, e.g., `btc_usdt`). The values stored for these fields are not immediately clear from the snippets but could be a simple flag (e.g., 1) or timestamp.
* When a trading pair is brought **online**, its `tradingKey` is removed (using `HDEL`) from the corresponding `coin_currency_offline_hash[...]` key(s).
* The system checks if a `coin_currency_offline_hash:<coin_key>:<currency_str>` hash becomes empty after a deletion. This suggests that if all trading pairs for a specific coin against a specific currency are brought online (or removed entirely), this offline tracking key might be cleaned up or trigger other logic.
* The `_dev` suffix indicates keys used in the development environment.

**Primary Logic Location & Files:**

* `app/Traits/TradingPairTrait.php`: This trait contains the primary logic for managing these keys. Specifically, when a trading pair's online/offline status is changed (e.g., during an `online` operation for a pair), entries are deleted from these hashes.

**Related Keys:**

* `coin_currency:<coin_key>`: Stores the list of online pricing currencies for a coin.
* `app_coin_offline_markets:<coin_key>`: Stores offline markets for a coin.
* `coin_currency_offline_list:<coin_key>`: (Potentially related) Might store a list of offline currencies for a coin, possibly as a sorted set if ordering is important.

---

## Key Prefix: `coin_currency_offline_hash_dev`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* This prefix is used in the **development environment** to store information about trading pairs that have been taken offline. It functions as a Redis Hash.
* The key pattern is typically `coin_currency_offline_hash_dev:<coin_type>` or `coin_currency_offline_hash_dev:<coin_type>:<currency_str>`.
  * `<coin_type>`: Identifier for the base cryptocurrency.
  * `<currency_str>`: Identifier for the quote currency.
* The fields within the hash are the trading pair keys, and their values might be timestamps of when they were taken offline or a simple flag.
* When a trading pair is brought online in the development environment, its entry is removed from this hash.
* Associated logic can be found in `app/Traits/TradingPairTrait.php` and `app/Console/Commands/TradingBatchOff.php`, which manage the online/offline status of trading pairs in the development environment.

---

## Key Prefix: `coin_currency_offline_list`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern & Usage:**
This prefix is used to manage lists of quote currencies that are entirely offline for a specific base cryptocurrency.

* **Data Structure:** Redis Sorted Set.
* **Key Pattern:** `coin_currency_offline_list:<coin_type>` (for production) and `coin_currency_offline_list_dev:<coin_type>` (for development environment).
  * `<coin_type>`: The identifier for the base cryptocurrency (e.g., `btc`).
* **Members:** The members of the sorted set are `<currency_str>` identifiers (e.g., `usdt`, `eth`).
* **Score:** The score associated with each member is a timestamp, likely representing the time when the last trading pair for that `coin_type/currency_str` combination was taken offline, effectively making that entire quote currency offline for the base coin.

**Detailed Logic:**

* When a trading pair (e.g., BTC/USDT) is taken offline, the system checks if there are any other online trading pairs for the same base coin and quote currency (BTC/USDT).
* If no other online pairs exist for that `coin_type` and `currency_str` (meaning all trading pairs for BTC using USDT as quote are now offline), the `currency_str` (`usdt`) is added to the sorted set `coin_currency_offline_list:<coin_type>` (e.g., `coin_currency_offline_list:btc`) with the current timestamp.
* Conversely, when a trading pair is brought online, if its `currency_str` was previously in the `coin_currency_offline_list:<coin_type>` set (meaning that quote currency was entirely offline for that base coin), the `currency_str` is removed from the set.
* This mechanism helps in efficiently querying which quote currencies are completely unavailable for trading against a specific base coin.

---

## Key Prefix: `coin_currency_offline_list_dev`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Pattern & Usage:**
This prefix is the **development environment** counterpart to `coin_currency_offline_list`.

* **Data Structure:** Redis Sorted Set.
* **Key Pattern:** `coin_currency_offline_list_dev:<coin_type>`
  * `<coin_type>`: The identifier for the base cryptocurrency (e.g., `btc`).
* **Members:** The members of the sorted set are `<currency_str>` identifiers (e.g., `usdt`, `eth`).
* **Score:** The score associated with each member is a timestamp, representing when the last trading pair for that `coin_type/currency_str` combination was taken offline in the development environment.

**Detailed Logic:**

* The logic mirrors that of `coin_currency_offline_list` but applies specifically to the development environment.
* When a trading pair is taken offline in the dev environment, if it's the last active pair for that base coin and quote currency, the quote currency is added to this sorted set for that base coin.
* When a trading pair is brought online in the dev environment, its quote currency is removed from this set if it was present.
* This helps track which quote currencies are entirely unavailable for a specific base coin within the development/testing setup.

**Primary Logic Location & Files:**

* `app/Traits/TradingPairTrait.php`: Contains the core logic, often using a `$dev` suffix (which would be `_dev`) to differentiate environments. Methods like `onlineMarketCoinConf` and `onlineHistory` handle these updates.
* `app/Console/Commands/TradingBatchOff.php`: Uses the trait to manage these lists during batch operations in the development environment.

---

## Key Prefix: `coin_list`

**Project:** `/Users/<USER>/back.aicoin.com`

**General Usage:**
This prefix is used to maintain lists of cryptocurrency coin identifiers (coin types) within the `conf` Redis database. It primarily appears in two forms:

1. **`coin_list` (General List of All Coins)**

   * **Data Structure:** Redis Set.
   * **Redis Connection:** `conf`.
   * **Key Pattern:** `coin_list`
   * **Purpose:** Stores a unique set of all coin type identifiers (e.g., `btc`, `eth`) that are known to the system.
   * **Logic:** When a new coin is introduced through trading pair configurations, its identifier is added to this set (e.g., via `SADD` in `app/Traits/TradingPairTrait.php`). If a coin is no longer part of any trading type, it might be removed.
2. **`coin_list:<trade_type>` (Coins by Trade Type)**

   * **Data Structure:** Redis Set.
   * **Redis Connection:** `conf`.
   * **Key Pattern:** `coin_list:<trade_type>` (e.g., `coin_list:spot`, `coin_list:futures`).
     * `<trade_type>`: Represents the type of trading, such as `spot`, `futures`, etc.
   * **Purpose:** Stores a set of coin type identifiers that are associated with a specific trade type. For example, `coin_list:spot` would contain all coins available for spot trading.
   * **Logic:** Managed primarily in `app/Traits/TradingPairTrait.php` and `app/Console/Commands/TradingBatchOff.php`. When the last trading pair of a specific `trade_type` for a coin is removed, the coin is removed from this set (`SREM`). Conversely, adding a coin to a `trade_type` adds it to this set.

**Related Coin List Keys:**
It's important to distinguish `coin_list` (as Sets in `conf`) from other related keys found in the codebase, which are often Sorted Sets and serve different listing or ranking purposes:

* `app_coin_list`: Typically a **Sorted Set** in the `master` Redis connection, used for an application-level ranked list of coins.
* `all_coin_list`: Typically a **Sorted Set** in the `conf` Redis connection, representing a comprehensive, ranked list of all coins.
* `online_coin_list` / `online_coin_list_dev`: **Sorted Sets** in `conf` Redis for listing coins that are online in production and development environments, respectively.

**Primary Logic Location & Files for `coin_list` prefix:**

* `app/Traits/TradingPairTrait.php`: Core logic for adding/removing coins from `coin_list` and `coin_list:<trade_type>` based on trading pair status.

---

## Key Prefix: `coin_market`

**Project:** `/Users/<USER>/back.aicoin.com`

**Usage:**
While `coin_market` itself does not appear as a direct Redis key prefix (e.g., `coin_market:*`) in the codebase, it represents the relationship between coins and markets. This relationship is primarily managed through the following Redis key patterns:

1. **`app_coin_markets:<coin_key>`**

   * **Data Structure**: Redis Sorted Set.
   * **Redis Connection**: `master`.
   * **Purpose**: Stores a list of market keys (exchanges) where the specified `<coin_key>` is traded. The score might be used for ordering or represent a timestamp.
   * **Primary Logic**: Found in `app/Traits/CoinTrait.php` (e.g., `getCoinMarket` method) and `app/Traits/CoinApplyTrait.php`. These traits handle fetching and managing the markets associated with a coin.
2. **`app_market_coins:<market_key>`**

   * **Data Structure**: Redis Sorted Set.
   * **Redis Connection**: `master`.
   * **Purpose**: Stores a list of coin keys (or trading pair keys) available on the specified `<market_key>`. The score is likely used for ordering.
   * **Primary Logic**: Found in `app/Traits/AnalysisTrait.php` (e.g., `getMarketCoinList` method) and `app/Traits/MarketTrait.php`. This is also detailed under the `app_market` prefix analysis in this report.

**Files Found (related to the implementing keys):**

* `app/Traits/CoinTrait.php`
* `app/Traits/CoinApplyTrait.php`
* `app/Traits/MarketTrait.php`
* `app/Traits/AnalysisTrait.php`
* `app/Http/Controllers/MarketCoinController.php` (existence noted, likely interacts with these concepts)

---

## Key Prefix: `coin_platform_all_v2`

**Project:** `/Users/<USER>/back.aicoin.com` (indirectly, or other projects)

**Usage (Inferred):**

* The prefix `coin_platform_all_v2` is present in the `redis_top_level_prefixes_sorted.csv` file, indicating its existence in the Redis data dump.
* Searches (semantic and grep) within the `/Users/<USER>/back.aicoin.com` PHP codebase did not yield direct programmatic usage of this prefix.
* This suggests that `coin_platform_all_v2` might be:
* A key managed by a different project or microservice within the ecosystem (e.g., `sosobtc_process`, `kline-data-scrapy`, `php-task`).
* A key used by data processing scripts or batch jobs that are not part of the main `back.aicoin.com` application logic.
* A legacy key that is no longer actively written to or read by the `back.aicoin.com` PHP application but still exists in Redis.
* Manually created or maintained for specific analytical or operational purposes.
* Without further context from other projects or a deeper understanding of the data flow across all services, its exact purpose and data structure (e.g., Hash, Set, List, Sorted Set, String) managed under this prefix cannot be definitively determined from the `back.aicoin.com` codebase alone.

**Files Found:**

* No specific PHP files in `/Users/<USER>/back.aicoin.com` were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `coin_trading` (and `coin_trading_spot`, `coin_trading_futures`)

**Projects:** `php-task`, `sosobtc_process`, `kline-data-scrapy`

**Usage:**

* The `coin_trading` prefix and its specific variations (`coin_trading_spot:{coin}` and `coin_trading_futures:{type}:{coin}`) are used to store Redis Sets of trading pair keys associated with a particular coin.
* `coin_trading:{coin_type}`: Stores a general list of trading pairs for a coin type. Used for recommendations and existence checks.
* `coin_trading_spot:{coin}`: Stores spot trading pairs for a specific coin. Used in data statistics tasks and signal ranking.
* `coin_trading_futures:swap:{coin}`: Stores futures swap (perpetual) trading pairs for a specific coin. Used in data statistics tasks.
* `coin_trading_futures:delivery:{coin}`: Stores futures delivery trading pairs for a specific coin. Used in data statistics tasks.
* The `sosobtc_process/deprecated-task/checkCurrency.php` script contains extensive logic for managing these sets, including adding/removing trading pairs and differentiating between mining/non-mining related pairs under keys like `trade_coin_trading:{coin_currency}` or `{trade_type}_coin_trading:{coin_currency}`.

**Data Structure:** Redis Set (members are trading pair keys or similar identifiers).

**Examples from `redis_key_prefix_paths_sorted.csv`:**

* `coin_trading:futures:bitcoin`
* `coin_trading:spot:ethereum`
* `coin_trading_futures:swap:bitcoin`
* `coin_trading_futures:delivery:ethereum`

**Files Found:**

* `php-task/app/Task/Data/MarketSearchHotRecommendTask.php`
* `php-task/app/Task/Data/CoinDataToEsTask.php`
* `php-task/app/Task/Data/CoinTradingPairStatTask.php`
* `sosobtc_process/deprecated-task/checkCurrency.php`
* `kline-data-scrapy/apps/Signal/ranking.php`

---

## Key Prefix: `conf_market_coin`

**Project:** Potentially external or data-pipeline related; no direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The prefix `conf_market_coin:` is used as a namespace, likely within the `conf` Redis database, to store coin-related configurations that are specific to a particular market (exchange).
* Keys often follow patterns like:
  * `conf_market_coin:<market_key>:` (e.g., `conf_market_coin:binance:`)
  * `conf_market_coin:<trade_type>:<market_key>:` (e.g., `conf_market_coin:spot:coinbase:`, `conf_market_coin:futures:okex:`)
* This structure implies that these keys store configurations for coins available on a specific market, possibly with variations for different trading types (spot, futures, options).
* The exact data structure (e.g., Hash, Set) and the specific attributes stored are not determinable from the PHP codebase, as no direct interactions were found.
* The data for these keys might be populated and consumed by data import/processing pipelines, other microservices, or manual configuration processes.

**Examples from `redis_key_prefix_paths_sorted.csv`:**

* `conf_market_coin:abcc:`
* `conf_market_coin:spot:bithumb:`
* `conf_market_coin:futures:bitmex:`

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `conf_market_list`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure and Related Code):**

* The `redis_key_prefix_paths_sorted.csv` file indicates the existence of keys such as `conf_market_list`, `conf_market_list:futures:`, `conf_market_list:option:`, `conf_market_list:other:`, and `conf_market_list:spot:`. These keys are likely stored in the `conf` Redis database.
* PHP code within the `back.aicoin.com` project manages market lists using different key names, primarily:
  * `all_market_list` (Sorted Set in the `conf` Redis): Stores a comprehensive list of all markets.
  * `online_market_list` (Sorted Set in the `conf` Redis): Stores markets that are online in the production environment.
  * `online_market_list_dev` (Sorted Set in the `conf` Redis): Stores markets that are online in the development environment.
  * `app_market_list` (Sorted Set in the `master` Redis): Stores an application-level list of markets.
* No PHP code was found to directly read from or write to Redis keys starting with the exact prefix `conf_market_list`.
* This suggests that the `conf_market_list:*` keys found in the Redis data dump are likely:
  * Legacy keys that are no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts, or data pipelines not covered in this analysis.
  * Potentially an older or alternative naming convention for market lists that has since been superseded by keys like `all_market_list` within the `conf` Redis database.

**Primary Logic Location & Files (for related market list management):**

* `back.aicoin.com/app/Traits/MarketTrait.php`: Contains logic for managing `all_market_list`, `online_market_list`, `online_market_list_dev` in the `conf` Redis.
* `back.aicoin.com/app/Traits/MarketApplyTrait.php`: Interacts with these market lists during market application and approval processes.
* `back.aicoin.com/app/Console/Commands/AttributeAdd.php`: Updates these lists when attributes are added or modified.
* `back.aicoin.com/app/Traits/AnalysisTrait.php`: Uses `app_market_list` (from `master` Redis) for analytical purposes.

---

## Key Prefix: `conf_trading_list`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data_scrapy`, `php-task`).

**Usage (Inferred from Key Structure and Related Code):**

* The `redis_key_prefix_paths_sorted.csv` file indicates the existence of keys such as `conf_trading_list`, `conf_trading_list:futures:`, `conf_trading_list:option:`, `conf_trading_list:other:`, and `conf_trading_list:spot:`. These keys are stored in the `conf` Redis database and appear to represent lists of trading pairs, categorized by trading type.
* PHP code within `back.aicoin.com/app/Traits/TradingPairTrait.php` (specifically the `marketCoinConf` method) extensively manages lists of trading pairs in the `conf` Redis database. However, it uses different key naming conventions:
  * `market_trading:<trade_type>:<market_key>` (Set: list of trading pairs for a specific market and trade type)
  * `market_trading:<market_key>` (Set: list of trading pairs for a specific market, all trade types)
  * `coin_trading:<trade_type>:<coin_key>` (Set: list of trading pairs for a specific coin and trade type)
  * `coin_trading:<coin_key>` (Set: list of trading pairs for a specific coin, all trade types)
  * `trading:<trade_type>:<coin_key>:<market_key>` (Set: list of trading pairs for a specific coin, market, and trade type)
  * `trading:<coin_key>:<market_key>` (Set: list of trading pairs for a specific coin and market, all trade types)
* No PHP code was found to directly read from or write to Redis keys starting with the exact prefix `conf_trading_list`.
* This suggests that the `conf_trading_list:*` keys found in the Redis data dump are likely:
  * Legacy keys that are no longer actively managed by the `marketCoinConf` logic in `TradingPairTrait.php`.
  * Managed by other external systems, scripts, or data pipelines not covered in this analysis.
  * An older or alternative naming convention for lists of trading pairs. The functionality of storing categorized trading pair lists is handled by the `market_trading:*`, `coin_trading:*`, and `trading:*` key patterns within the `conf` Redis database as per the current PHP codebase.

**Primary Logic Location & Files (for related trading pair list management in `conf` Redis):**

* `back.aicoin.com/app/Traits/TradingPairTrait.php`: The `marketCoinConf` method is central to creating and maintaining various sets that act as lists of trading pairs, markets, and coins, segmented by trade type, market, and coin.

---

## Key Prefix: `config`

**Projects:** No widespread, direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`) for managing general application configurations under a `config:` Redis key prefix.

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates that `config` is a top-level prefix, meaning keys starting with `config` (e.g., `config:some_setting_name` or the key `config` itself) exist in the Redis dataset.
* Code analysis (grep and semantic searches) did not reveal a common pattern in PHP where a `config:` prefix is used to store or retrieve general application settings from Redis.
* The `back.aicoin.com` project defines URL routes under `/config` (e.g., for `UnusualConfigController` in `routes/needLogin/config.php`), but this is an API endpoint prefix, not a Redis key prefix.
* The `UnusualConfigController.php` itself uses a specific Redis hash key `task:unusual_action:configs` for its settings, not a key under a general `config:` prefix.
* The term `conf` is extensively used as a name for a Redis *database connection* (defined in `config/database.php` and accessed via `RedisClient::connection('conf')`). This `conf` connection stores various application configurations, but these are typically under other, more specific key prefixes (e.g., `app:*`, `all_market_list`, `market_trading:*`).
* Therefore, Redis keys starting with the `config` prefix are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by external systems, other microservices, or data pipelines.
  * Accessed by their full, specific key names if used at all by PHP, rather than through a generic `config:*` pattern.

**Relevant Files (Indirectly Related or Illustrative):**

* `back.aicoin.com/app/Http/Controllers/Config/UnusualConfigController.php`: Manages specific task configurations using the key `task:unusual_action:configs`.
* `back.aicoin.com/config/database.php`: Defines Redis connections, including the `conf` connection.
* `back.aicoin.com/routes/needLogin/config.php`: Defines API routes with a `/config` URL prefix.

---

## Key Prefix: `core_lib`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `core_lib` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file shows a specific key: `core_lib:holiday_cache:`.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `core_lib:`.
* This suggests that keys under the `core_lib` prefix, such as `core_lib:holiday_cache:`, are likely:
  * Legacy keys that are no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts, or data pipelines.
  * Potentially used to cache holiday schedules or other core, shared library configurations, but not by the PHP applications in scope.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `crypto`

**Projects:** No direct usage found as a Redis key prefix in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure and Code Analysis):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `crypto` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file shows the existence of a key named `crypto:`.
* A grep search found `'x-skip-crypto: 1'` in `kline-data-scrapy/libs/hotranks.php`, which is an HTTP header and not related to Redis key usage.
* Semantic searches did not reveal any specific PHP code that directly uses `crypto:` as a prefix for storing or retrieving data from Redis.
* This suggests that Redis keys starting with or named `crypto` are likely:
  * Legacy keys that are no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts, or data pipelines.
  * Used for a very specific purpose not evident in the analyzed PHP codebase.

**Files Found (Indirectly Related or Negative Findings):**

* `kline-data-scrapy/libs/hotranks.php` (contains `x-skip-crypto` HTTP header, not a Redis key).
* No PHP files within the searched projects were found to be directly interacting with a `crypto:` Redis key prefix.

---

## Key Prefix: `currency_info`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* The prefix `currency_info:` is used to store detailed information about currencies (likely fiat currencies or primary quote currencies like USDT, USD, CNY, rather than all tradable cryptocurrencies).
* Keys follow the pattern `currency_info:<currency_key>`, where `<currency_key>` is the unique identifier for the currency (e.g., `usdt`, `usd`, `cny`).
* These keys store **Redis Hashes**.
* The hash fields contain various attributes of the currency, such as its name, symbol, precision, type, or other configuration details.
* This information is managed (created, read, updated, deleted) in both `master` and `conf` Redis connections.

**Primary Logic Location & Files:**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/CurrencyManageController.php`:
  * Handles the CRUD (Create, Read, Update, Delete) operations for currency information.
  * `addCurrency`: Creates/updates the currency hash in both `master` and `conf` Redis.
  * `addAttribute`: Adds/updates a specific field within the currency hash in both Redis connections.
  * `delAttribute`: Logically removes an attribute by setting its value to an empty string in both Redis connections.
  * `delCurrency`: Deletes the entire currency hash from both Redis connections.
* `/Users/<USER>/sosobtc_process/lib/tradingPairTrait.php`:
  * The `getCurrencyInfo` method reads the currency hash using `hGetAll('currency_info:'.$currency)` from the default Redis connection (likely `master` or `conf` based on context within that project).

**Examples from `redis_key_prefix_paths_sorted.csv` (illustrative of potential keys):**

* `currency_info:usd`
* `currency_info:usdt`
* `currency_info:cny`

---

## Key Prefix: `dex_chain_detail`

**Project:** `/Users/<USER>/php-task`

**Usage:**

* The prefix `dex_chain_detail:` is used to cache detailed information about DEX (Decentralized Exchange) chains.
* Keys likely follow the pattern `dex_chain_detail:<chain_id>` or `dex_chain_detail:<chain_key>`, where `<chain_id>` or `<chain_key>` is the unique identifier for a specific DEX chain (e.g., `ethereum`, `bsc`, `polygon`).
* These keys store **Redis Hashes**.
* The hash fields contain various attributes of the DEX chain, such as its name, RPC URL, explorer URL, native currency symbol, chain ID, and other configuration details necessary for interacting with or displaying information about that chain.
* The primary purpose is to cache these details to avoid repeated database queries or external API calls when this information is needed.

**Primary Logic Location & Files:**

* `/Users/<USER>/php-task/app/Task/Config/DexConfigCacheTask.php`:
  * This task is responsible for fetching DEX chain configurations (likely from a primary database or configuration source) and caching them into Redis under the `dex_chain_detail:<chain_key>` pattern.
  * It defines `$chainDetailPrefixKey = "dex_chain_detail:";` and uses it to construct the specific Redis keys for each chain.
  * It iterates through chain data and uses `hMSet` to store the chain's attributes in the corresponding Redis hash.

**Examples from `redis_key_prefix_paths_sorted.csv` (illustrative of potential keys):**

* `dex_chain_detail:ethereum`
* `dex_chain_detail:bsc`
* `dex_chain_detail:arbitrum`

---
