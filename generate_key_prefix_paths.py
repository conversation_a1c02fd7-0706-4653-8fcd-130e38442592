import csv

def main():
    input_csv_file = 'redis_all_keys.csv'
    output_csv_file = 'redis_key_prefix_paths_sorted.csv'

    keys_list = []

    # 1. 从输入的CSV文件读取所有Key
    try:
        with open(input_csv_file, 'r', newline='', encoding='utf-8') as infile:
            reader = csv.reader(infile)
            header = next(reader, None)

            if header:
                if header[0].lower().strip() != 'key':  # 如果表头不是"key"，则将其视为一个key
                    key_to_add = header[0].strip()
                    if key_to_add:
                        keys_list.append(key_to_add)
            
            for row in reader:
                if row:
                    key_to_add = row[0].strip()
                    if key_to_add:
                        keys_list.append(key_to_add)
        
        if not keys_list:
            print(f"在 '{input_csv_file}' 文件中没有找到任何key。将创建一个空的输出文件。")
            with open(output_csv_file, 'w', newline='', encoding='utf-8') as outfile:
                pass 
            return

        print(f"从 '{input_csv_file}' 文件成功读取 {len(keys_list)} 个key。")

        # 对键列表进行去重
        unique_keys = list(set(keys_list))
        print(f"去重后剩余 {len(unique_keys)} 个唯一的key。")
        
        # 对去重后的键列表进行字典序排序
        unique_keys.sort()
        print("所有唯一的key已按字典序完成排序。")

    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_csv_file}' 未找到。")
        return
    except Exception as e:
        print(f"读取 '{input_csv_file}' 文件时发生错误: {e}")
        return

    # 2. 收集所有唯一的前缀路径
    all_prefixes = set()  # 使用集合自动去重

    for key_string in unique_keys:
        parts = key_string.split(':')
        num_segments = len(parts)
        
        if num_segments > 1:
            current_prefix_parts = []
            for i in range(num_segments - 1): 
                current_prefix_parts.append(parts[i])
                prefix_str = ":".join(current_prefix_parts)
                
                # 为最后一个段添加冒号后缀
                if i == (num_segments - 2):
                    prefix_str += ":"
                
                all_prefixes.add(prefix_str)  # 添加到集合中自动去重
    
    # 3. 将前缀路径转换为列表并排序
    sorted_prefixes = sorted(list(all_prefixes))
    print(f"生成 {len(sorted_prefixes)} 个唯一的前缀路径。")

    # 4. 准备输出数据 - 每行一个前缀路径
    output_rows = [[prefix] for prefix in sorted_prefixes]

    # 5. 将处理后的数据写入新的CSV文件
    try:
        with open(output_csv_file, 'w', newline='', encoding='utf-8') as outfile:
            writer = csv.writer(outfile)
            # 写入表头
            writer.writerow(["Prefix Path"])
            writer.writerows(output_rows)
        print(f"处理完成。结果已保存到 '{output_csv_file}' (已去重并排序)。")
    except Exception as e:
        print(f"写入 '{output_csv_file}' 文件时发生错误: {e}")

if __name__ == "__main__":
    main()