import csv
import collections

def build_prefix_tree(keys):
    """
    从key列表构建一个前缀树。
    树的每个节点是一个字典，key是前缀片段，value是子树。
    """
    tree = {}
    for key_string in keys:
        if not key_string.strip(): # 跳过空或只有空格的key
            continue
        parts = key_string.split(':')
        current_level_node = tree
        for part in parts:
            if part not in current_level_node:
                current_level_node[part] = {}
            current_level_node = current_level_node[part]
    return tree

def get_all_prefix_details(tree_node, path_segments, prefix_details_map):
    """
    递归遍历前缀树，收集每个前缀及其直接子前缀。
    path_segments: 到达当前节点的路径片段列表。
    prefix_details_map: 存储 { "完整前缀": ["子片段1", "子片段2", ...], ... }
    """
    if path_segments: # 只有当路径不为空时，才记录为一个有效前缀
        current_prefix_str = ":".join(path_segments)
        # 当前节点(tree_node)的keys就是当前前缀的直接子片段
        direct_sub_segments = sorted(list(tree_node.keys()))
        prefix_details_map[current_prefix_str] = direct_sub_segments

    # 对所有子节点进行递归
    for segment, sub_node in tree_node.items():
        get_all_prefix_details(sub_node, path_segments + [segment], prefix_details_map)

def main():
    input_csv_file = 'redis_all_keys.csv'
    output_txt_file = 'redis_key_prefix_analysis.txt'
    keys_list = []

    # 1. 从CSV文件读取Key
    try:
        with open(input_csv_file, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            header = next(reader, None) # 读取表头，如果文件为空则为None

            if header and header[0].lower().strip() == 'key':
                # 如果表头是'Key'（不区分大小写，去除两端空格），则从下一行开始读取
                pass
            elif header:
                # 如果有表头但不是预期的'Key'，则将表头也作为第一个key处理
                if header[0].strip(): # 确保不是空行
                    keys_list.append(header[0])
            
            for row in reader:
                if row and row[0].strip(): # 确保行和key本身不为空
                    keys_list.append(row[0])
        
        if not keys_list:
            print(f"'{input_csv_file}' 中没有找到任何key。")
            with open(output_txt_file, 'w', encoding='utf-8') as outfile:
                outfile.write("在CSV文件中没有找到任何key。\n")
            return

        print(f"从 '{input_csv_file}' 中成功读取 {len(keys_list)} 个key。")

    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_csv_file}' 未找到。")
        return
    except Exception as e:
        print(f"读取 '{input_csv_file}' 时发生错误: {e}")
        return

    # 2. 构建前缀树
    prefix_tree_root = build_prefix_tree(keys_list)

    # 3. 收集所有前缀及其子前缀的详细信息
    all_prefixes_with_children = collections.OrderedDict() # 使用有序字典保持输出顺序
    # 首先处理根节点，以启动递归收集过程
    get_all_prefix_details(prefix_tree_root, [], all_prefixes_with_children)
    
    # 排序，以确保输出是有序的（按完整前缀字符串排序）
    # get_all_prefix_details 本身会按 segment 字母序递归，但整体前缀顺序由 collections.OrderedDict + 之后排序保证
    # 如果要严格按层级和字母序，需要更复杂的排序，但目前按完整前缀字母序已足够清晰
    sorted_prefix_items = sorted(all_prefixes_with_children.items())


    # 4. 生成并写入文档
    output_lines = []
    output_lines.append("Redis Key 前缀层级分析报告\n")
    output_lines.append("=" * 40 + "\n")

    # 输出顶层前缀概览
    top_level_prefixes = sorted(list(prefix_tree_root.keys()))
    if top_level_prefixes:
        output_lines.append("顶层前缀 (Level 1):\n")
        for tl_prefix in top_level_prefixes:
            output_lines.append(f"- {tl_prefix}\n")
        output_lines.append("-" * 40 + "\n")
    else:
        output_lines.append("没有发现任何顶层前缀。\n")
        output_lines.append("-" * 40 + "\n")


    # 输出每个前缀及其直接子前缀的详细列表
    if not sorted_prefix_items:
         output_lines.append("未能提取任何前缀结构。\n")
    
    for full_prefix, direct_children_segments in sorted_prefix_items:
        output_lines.append(f"前缀 (Prefix): {full_prefix}\n")
        if direct_children_segments:
            output_lines.append("  直接子级前缀 (Direct Sub-prefixes):\n")
            for child_segment in direct_children_segments:
                output_lines.append(f"  - {child_segment}  (构成完整子前缀: {full_prefix}:{child_segment})\n")
        else:
            output_lines.append("  (该前缀无直接子级)\n")
        output_lines.append("\n") # 添加空行以提高可读性

    try:
        with open(output_txt_file, 'w', encoding='utf-8') as outfile:
            outfile.writelines(output_lines)
        print(f"分析完成！结果已保存到 '{output_txt_file}'。")
    except Exception as e:
        print(f"写入输出文件 '{output_txt_file}' 时发生错误: {e}")

if __name__ == "__main__":
    main()