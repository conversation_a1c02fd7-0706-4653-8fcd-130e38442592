## Key Prefix: `fund_conf_base`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `fund_conf_base` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix, such as `fund_conf_base:btcnextweek:`, `fund_conf_base:btcnwusd:`, `fund_conf_base:btcpcusdt:`, `fund_conf_base:ethswapusdt:`, etc.
* The pattern of these keys suggests they store base configuration data for various financial instruments, likely related to funds, futures contracts (e.g., "nextweek", "quarter"), or specific trading pairs in the context of fund management.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `fund_conf_base:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts, or data pipelines responsible for fund data configuration and management.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_dbkey_to_elite`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_dbkey_to_elite` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix, such as `kline_dbkey_to_elite:1000000mogswapusdt:`, `kline_dbkey_to_elite:btcnextweekusdt:`, `kline_dbkey_to_elite:ethswapusdt:`, etc.
* The pattern of these keys suggests they store a mapping from a "dbkey" (database key, likely representing a specific trading pair or instrument) to an "elite" representation or identifier.
* The presence of the reverse mapping `kline_elite_to_dbkey` further supports this, indicating a bidirectional relationship.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_dbkey_to_elite:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data and its various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_elite_to_dbkey`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_elite_to_dbkey` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_elite_to_dbkey:1000000mog:`, `kline_elite_to_dbkey:btc:`, `kline_elite_to_dbkey:eth:`). The pattern suggests these keys map an "elite" identifier (a simplified or standardized representation of a trading pair or instrument) back to its corresponding "dbkey" (database key, which may include additional information like exchange or specific formatting).
* This prefix represents the reverse mapping of `kline_dbkey_to_elite`, forming a bidirectional relationship that allows the system to convert between these two representations efficiently.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_elite_to_dbkey:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data and its various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_dbkey_to_loanratio`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_dbkey_to_loanratio` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_dbkey_to_loanratio:memefiusdt:`, `kline_dbkey_to_loanratio:wifusdt:`). The pattern suggests these keys map a "dbkey" (likely representing a specific trading pair or instrument) to its corresponding "loan ratio" data.
* The existence of a corresponding top-level prefix `kline_loanratio_to_dbkey` (also listed in `redis_top_level_prefixes_sorted.csv`) strongly suggests a bidirectional mapping.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_dbkey_to_loanratio:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, loan ratios, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_lspr_to_dbkey`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_lspr_to_dbkey` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_lspr_to_dbkey:1000000mog:`, `kline_lspr_to_dbkey:b3swap:`, `kline_lspr_to_dbkey:btcusdt:`). The pattern suggests these keys map an "LSPR" (Long/Short Position Ratio or similar metric) identifier back to its corresponding "dbkey" (database key, which likely represents a specific trading pair or instrument).
* This prefix represents the reverse mapping of `kline_dbkey_to_lspr`, forming a bidirectional relationship that allows the system to convert between database keys and LSPR identifiers efficiently.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_lspr_to_dbkey:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, LSPR information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_dbkey_to_oiav`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_dbkey_to_oiav` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_dbkey_to_oiav:1inchswapusd:`, `kline_dbkey_to_oiav:btcswapusdt:`). The pattern suggests these keys map a "dbkey" (likely representing a specific trading pair or instrument) to its corresponding "OIAV" data (the exact meaning of OIAV, possibly Open Interest Aggregated Volume or a similar metric, is not explicitly defined in the provided context but is K-line related).
* The existence of a corresponding top-level prefix `kline_oiav_to_dbkey` (also listed in `redis_top_level_prefixes_sorted.csv` and with keys in `redis_key_prefix_paths_sorted.csv`) strongly suggests a bidirectional mapping.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_dbkey_to_oiav:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, OIAV information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_dbkey_to_ttv`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_dbkey_to_ttv` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_dbkey_to_ttv:1incheur:`, `kline_dbkey_to_ttv:btcusdt:`). The pattern suggests these keys map a "dbkey" (likely representing a specific trading pair or instrument) to its corresponding "TTV" data (the exact meaning of TTV, possibly Total Trading Volume or a similar metric, is not explicitly defined in the provided context but is K-line related).
* The existence of a corresponding top-level prefix `kline_ttv_to_dbkey` (also listed in `redis_top_level_prefixes_sorted.csv`) strongly suggests a bidirectional mapping.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_dbkey_to_ttv:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, TTV information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_loanratio_to_dbkey`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_loanratio_to_dbkey` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (e.g., `kline_loanratio_to_dbkey:ada:`, `kline_loanratio_to_dbkey:btc:`, `kline_loanratio_to_dbkey:eth:`). The pattern suggests these keys map a "loan ratio" identifier back to its corresponding "dbkey" (database key, which likely represents a specific trading pair or instrument).
* This prefix represents the reverse mapping of `kline_dbkey_to_loanratio`, forming a bidirectional relationship that allows the system to convert between database keys and loan ratio identifiers efficiently.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_loanratio_to_dbkey:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, loan ratio information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_oiav_to_dbkey`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_oiav_to_dbkey` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists numerous specific keys under this prefix (examples likely include coin/token identifiers like `kline_oiav_to_dbkey:btc:`, `kline_oiav_to_dbkey:eth:`). The pattern suggests these keys map an "OIAV" (Open Interest Aggregated Volume or similar metric) identifier back to its corresponding "dbkey" (database key, which likely represents a specific trading pair or instrument).
* This prefix represents the reverse mapping of `kline_dbkey_to_oiav`, forming a bidirectional relationship that allows the system to convert between database keys and OIAV identifiers efficiently.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_oiav_to_dbkey:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, OIAV information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `kline_ttv_to_dbkey`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `kline_ttv_to_dbkey` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file likely lists numerous specific keys under this prefix (similar to other reverse mappings seen, these would include coin/token identifiers like `kline_ttv_to_dbkey:btc:`, `kline_ttv_to_dbkey:eth:`). The pattern suggests these keys map a "TTV" (likely Total Trading Volume or similar metric) identifier back to its corresponding "dbkey" (database key, which represents a specific trading pair or instrument).
* This prefix represents the reverse mapping of `kline_dbkey_to_ttv`, forming a bidirectional relationship that allows the system to convert between database keys and TTV identifiers efficiently.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `kline_ttv_to_dbkey:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts (potentially Python scripts in `redis-analysis` or other data processing pipelines), or data pipelines responsible for K-line data, TTV information, and their various representations.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `liquidation`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `liquidation` as a top-level prefix.
* The `redis_key_prefix_paths_sorted.csv` file lists specific keys under this prefix that follow patterns such as:
  * `liquidation:<coin>` (e.g., `liquidation:btc`, `liquidation:eth`) - Organizing liquidation data by cryptocurrency
  * `liquidation:<coin>:<exchange>:` (e.g., `liquidation:btc:hbdm:`) - Organizing by cryptocurrency and specific exchange
  * `liquidation:<trading_pair>` (e.g., `liquidation:btcusdt`) - Organizing by trading pair
  * `liquidation:<trading_pair>:<exchange>:` (e.g., `liquidation:btcusdt:okex:`) - Organizing by trading pair and exchange
* These patterns suggest that keys with the `liquidation` prefix store data related to liquidation events (forced closure of positions due to insufficient margin) in cryptocurrency futures/margin trading, organized by coin, trading pair, and exchange.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with `liquidation:`.
* This implies that these keys are likely:
  * Legacy keys no longer actively used by the current PHP applications.
  * Managed by other external systems, scripts, or data pipelines specifically designed for tracking and analyzing liquidation events in cryptocurrency markets.
  * Used by parts of the system not included in this PHP codebase analysis.

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.

---

## Key Prefix: `market_coin`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* The prefix `market_coin:` is used to store lists of coins available on specific markets (exchanges).
* Keys follow these main patterns:
  * `market_coin:<market_key>` - A Redis Set storing all coin identifiers available on a specific market.
  * `market_coin:<trade_type>:<market_key>` - A Redis Set storing coin identifiers for a specific trade type (e.g., `futures`, `spot`) on a specific market.
* These Redis Sets are part of a broader relationship management system between markets, coins, and trading pairs:
  * When a trading pair is added to the system, the corresponding coin is added to the market's coin list.
  * When the last trading pair for a specific coin is removed from a market, the coin is removed from the market's coin list.
* These keys help efficiently answer queries like "which coins are available on exchange X?" or "which coins can be traded in futures on exchange Y?"

**Primary Logic Location & Files:**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: Contains the core logic for managing these sets within the `marketCoinConf` method and related methods like `deleteMarketCoinConf`.
  * The method adds coin identifiers to these sets when creating new trading pairs.
  * It removes coins from these sets when the last trading pair for that coin on that market is deleted.
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: Uses these sets during batch operations to manage market-coin relationships.
* `/Users/<USER>/sosobtc_process/service/show/BinanceTrading.php`: Reads from these keys to get lists of coins available on specific markets.
* `/Users/<USER>/sosobtc_process/task/MarketCoinCount.php`: Uses these keys to count market-coin relationships.

**Related Keys:**

* `market_list`: A Redis Set storing all available market keys.
* `coin_market:<coin_key>`: The inverse relationship, storing markets where a specific coin is available.
* `market_trading:<market_key>`: A Redis Set storing all trading pairs available on a specific market.

**Examples (from code usage):**

* `market_coin:binance` - All coins available on Binance
* `market_coin:futures:okex` - All coins available for futures trading on OKEx

---

## Key Prefix: `market_currency`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* The prefix `market_currency:` is used to store lists of quote currencies (pricing currencies, e.g., USDT, BTC) available on specific markets (exchanges).
* Keys follow these main patterns:
  * `market_currency:<market_key>` - A Redis Sorted Set storing quote currency identifiers available on a specific market (all trade types).
  * `market_currency:<trade_type>:<market_key>` - A Redis Sorted Set storing quote currency identifiers for a specific trade type (e.g., `futures`, `spot`) on a specific market.
* These Redis Sorted Sets are used within a broader system of trading pair management:
  * When a trading pair is added to the system, the corresponding quote currency is added to the market's currency list if it wasn't already present.
  * When the last trading pair for a specific quote currency is removed from a market, the quote currency is removed from the market's currency list.
* These keys help efficiently answer queries like "which quote currencies are available on exchange X?" or "which quote currencies can be used for futures trading on exchange Y?"
* The score in the sorted set is typically used for ordering or represents a timestamp of when the currency was added.

**Primary Logic Location & Files:**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: Contains the core logic for managing these sorted sets within the `marketCoinConf` method and related methods:
  * Adds quote currencies to these sets when creating new trading pairs via `RedisClient::zadd("conf", 'market_currency:<trade_type>:<market_key>', $score, $currency_str)`.
  * Removes quote currencies from these sets when the last trading pair for that quote currency on that market is deleted via `$conf_redis->ZREM('market_currency:<trade_type>:<market_key>', $currency_str)`.
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: Uses these sets during batch operations to manage market-currency relationships.
* `/Users/<USER>/sosobtc_process/lib/tradingPairTrait.php`: Similar to the back.aicoin.com version, manages these sets when trading pairs are created or deleted.
* `/Users/<USER>/sosobtc_process/task/dataMarketSpotNonMiningCurr.php` and related tasks: Read from these keys to determine available quote currencies for specific markets.

**Related Keys:**

* `market_trading:<market_key>:<currency_str>`: A Redis Set storing trading pairs for a specific market and quote currency.
* `coin_currency:<coin_key>`: A Redis Sorted Set storing quote currencies available for a specific coin.
* `currency_info:<currency_key>`: A Redis Hash storing detailed information about a specific quote currency.

**Examples (from code usage):**

* `market_currency:binance` - All quote currencies available on Binance
* `market_currency:futures:okex` - All quote currencies available for futures trading on OKEx

---

## Key Prefix: `market_list`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* The prefix `market_list:` is used to store lists of markets (exchanges) categorized by trading type.
* Keys follow these main patterns:
  * `market_list` - A Redis Set storing all market identifiers available in the system.
  * `market_list:<trade_type>` - A Redis Set storing market identifiers that support a specific trade type (e.g., `spot`, `futures`).
* These Redis Sets are part of the broader trading pair management system:
  * When a trading pair is added to the system for a specific market and trade type, the market is added to the corresponding market list if it wasn't already present.
  * When the last trading pair of a specific trade type is removed from a market, the market may be removed from the corresponding market list.
* These keys help efficiently answer queries like "which exchanges support futures trading?" or "which exchanges are available in the system?"

**Primary Logic Location & Files:**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: Contains the core logic for managing these sets within the `marketCoinConf` method and related methods:
  * Adds markets to these sets when creating new trading pairs via `$conf_redis->SADD('market_list:<trade_type>', $market_type)`.
  * Removes markets from these sets when the last trading pair for that market and trade type is deleted via `$conf_redis->SREM('market_list:<trade_type>', $market_type)`.
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: Uses these sets during batch operations to manage market lists.
* `/Users/<USER>/sosobtc_process/lib/tradingPairTrait.php`: Similar to the back.aicoin.com version, manages these sets when trading pairs are created or deleted.
* `/Users/<USER>/sosobtc_process/task/dataMarketCount.php`: Reads from these keys to count markets by trading type.

**Related Keys:**

* `all_market_list`: A Redis Sorted Set in the `conf` Redis database storing all markets with their ranks.
* `online_market_list`: A Redis Sorted Set storing markets that are online in the production environment.
* `online_market_list_dev`: A Redis Sorted Set storing markets that are online in the development environment.
* `market_coin:<market_key>`: A Redis Set storing coins available on a specific market.
* `market_trading:<market_key>`: A Redis Set storing all trading pairs available on a specific market.

**Examples (from code usage):**

* `market_list` - All markets in the system
* `market_list:spot` - All markets that support spot trading
* `market_list:futures` - All markets that support futures trading

---

## Key Prefix: `market_mining`

**Projects:** No direct usage found in the searched PHP projects for the exact prefix, but related concepts exist in `/Users/<USER>/sosobtc_process`.

**Usage (Inferred from Key Structure and Related Code):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `market_mining` as a top-level prefix.
* Related prefixes like `trade_mining_market_currency:` are used in deprecated tasks in `sosobtc_process/deprecated-task/dataMarketTradeMiningCurr.php`.
* The pattern suggests that keys with the `market_mining:` prefix are likely used to store information about mining incentives or rewards programs on specific exchanges, where users can earn tokens by trading (transaction mining or trade mining).
* These keys likely store:
  * Which markets (exchanges) offer mining incentives
  * Configuration details for mining programs on each market
  * Possibly statistics about mining rewards or rates
* The existence of `market_mining_component` as another top-level prefix (analyzed separately) further supports this interpretation.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with the exact prefix `market_mining:`.
* This implies that these keys are likely:
  * Legacy keys from when more exchanges offered trade mining programs (which were popular around 2018-2019)
  * Managed by specialized systems or scripts for tracking mining incentives
  * Possibly still in use but managed by code not included in the analyzed projects

**Related Keys:**

* `market_mining_component:<market_key>:` - Stores components or specific aspects of mining programs for a given market
* `trade_mining_market_currency:<market_key>` - Stores currencies involved in trading mining on a specific market (used in deprecated code)

**Files Found (For Related Concepts):**

* `/Users/<USER>/sosobtc_process/deprecated-task/dataMarketTradeMiningCurr.php` - Uses related keys like `trade_mining_market_currency:<market>`
* `/Users/<USER>/sosobtc_process/deprecated-task/checkCurrency.php` - Contains logic for maintaining mining-related Redis keys

---

## Key Prefix: `market_mining_component`

**Projects:** No direct usage found in the searched PHP projects for the exact prefix, but related to the `market_mining` concept found in `/Users/<USER>/sosobtc_process`.

**Usage (Inferred from Key Structure and Related Code):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `market_mining_component` as a top-level prefix.
* This prefix appears to be closely related to the `market_mining` prefix, likely representing the components or specific aspects of mining incentive programs on cryptocurrency exchanges.
* The pattern suggests that keys with the `market_mining_component:` prefix might store detailed configuration or data about specific aspects of transaction mining (trade mining) programs, such as:
  * Mining rates for different trading pairs
  * Mining rules or algorithms
  * Component tokens or assets involved in the mining program
  * Timeframes or periods for mining rewards
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with the exact prefix `market_mining_component:`.
* This suggests that these keys are likely:
  * Components of a larger system for managing transaction mining programs
  * Legacy keys from when more exchanges offered trade mining programs (popular around 2018-2019)
  * Managed by specialized systems or scripts not included in the analyzed PHP projects

**Related Keys:**

* `market_mining:<market_key>:` - Likely stores general information about mining programs on specific markets
* `trade_mining_market_currency:<market_key>` - Stores currencies involved in trading mining on a specific market (found in deprecated code)

**Files Found (For Related Concepts):**

* `/Users/<USER>/sosobtc_process/deprecated-task/dataMarketTradeMiningCurr.php` - Contains logic for trade mining currency management
* `/Users/<USER>/sosobtc_process/deprecated-task/checkCurrency.php` - Contains logic for maintaining mining-related Redis keys

---

## Key Prefix: `market_stat_currency`

**Projects:** `/Users/<USER>/php-task`

**Usage:**

* The prefix `market_stat_currency:` is used to store lists of categorized quote currencies available on specific markets (exchanges).
* Keys follow the pattern `market_stat_currency:<market_key>` - A Redis Set storing specialized quote currency category identifiers available on a specific market.
* Unlike the more general `market_currency:` prefix which simply lists available quote currencies, this prefix stores categorized versions of quote currencies that differentiate between spot and futures markets.
* The set members follow these naming patterns:
  * `{currency_str}_not_futures` - For spot trading pairs (non-futures) using a specific quote currency
  * `{currency_str}_swap` - For perpetual swap contracts using a specific quote currency
  * `{currency_str}_delivery` - For delivery futures contracts using a specific quote currency
* For example, a market supporting USDT-quoted spot trading and USDT-quoted perpetual contracts would have both `usdt_not_futures` and `usdt_swap` in its `market_stat_currency:{market}` set.
* This categorization helps the system efficiently filter trading pairs by both quote currency and contract type simultaneously.

**Primary Logic Location & Files:**

* `/Users/<USER>/php-task/app/Task/Data/MarketCurrencyStatTask.php`: Contains the core logic for managing these sets:
  * The task runs hourly to update these statistics.
  * It processes four main quote currencies: USDT, USD, USDC, and FDUSD.
  * For each market and currency, it categorizes trading pairs and updates corresponding `market_trading:{market}:{category}` sets.
  * It then stores the list of available categories in the `market_stat_currency:{market}` set.

**Related Keys:**

* `market_trading:{market}:{currency_str}`: A Redis Set storing all trading pairs for a specific market and quote currency.
* `market_trading:{market}:{currency_str}_not_futures`: A Redis Set storing spot trading pairs for a specific market and quote currency.
* `market_trading:{market}:{currency_str}_swap`: A Redis Set storing perpetual swap contracts for a specific market and quote currency.
* `market_trading:{market}:{currency_str}_delivery`: A Redis Set storing delivery futures contracts for a specific market and quote currency.

**Examples (from code usage):**

* `market_stat_currency:binance` - Might contain entries like `usdt_not_futures`, `usdt_swap`, `usdc_not_futures`
* `market_stat_currency:okex` - Might contain entries like `usdt_not_futures`, `usdt_swap`, `usdt_delivery`

---

## Key Prefix: `market_trading`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`, `/Users/<USER>/php-task`

**Usage:**

* The prefix `market_trading:` is used to store lists of trading pairs available on specific markets (exchanges).
* Keys follow these main patterns:
  * `market_trading:<market_key>` - A Redis Set storing all trading pair identifiers available on a specific market.
  * `market_trading:<trade_type>:<market_key>` - A Redis Set storing trading pairs for a specific trade type (e.g., `spot`, `futures`) on a specific market.
  * `market_trading:<market_key>:<currency_str>` - A Redis Set storing trading pairs for a specific market and quote currency (e.g., USDT, BTC).
  * `market_trading:<trade_type>:<market_key>:<currency_str>` - A Redis Set storing trading pairs for a specific trade type, market, and quote currency.
* These Redis Sets are central to the trading pair management system:
  * When a trading pair is added to the system, it is added to the appropriate market trading sets.
  * When a trading pair is removed, it is removed from these sets.
  * These sets are frequently used to quickly retrieve all trading pairs available on a specific market, optionally filtered by trade type or quote currency.
* The pattern also extends to specialized categorizations created by the `MarketCurrencyStatTask`:
  * `market_trading:<market_key>:<currency_str>_not_futures` - Spot trading pairs for a specific market and quote currency.
  * `market_trading:<market_key>:<currency_str>_swap` - Perpetual swap contracts for a specific market and quote currency.
  * `market_trading:<market_key>:<currency_str>_delivery` - Delivery futures contracts for a specific market and quote currency.

**Primary Logic Location & Files:**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: Contains the core logic for managing these sets within the `marketCoinConf` method and related methods:
  * Adds trading pairs to these sets when creating new trading pairs via `$conf_redis->SADD('market_trading:<trade_type>:<market_type>', $trading_key)`.
  * Removes trading pairs from these sets when they are deleted via `$conf_redis->SREM('market_trading:<trade_type>:<market_type>', $trading_key)`.
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: Uses these sets during batch operations to manage trading pairs.
* `/Users/<USER>/sosobtc_process/lib/tradingPairTrait.php`: Similar to the back.aicoin.com version, manages these sets when trading pairs are created or deleted.
* `/Users/<USER>/php-task/app/Task/Data/MarketCurrencyStatTask.php`: Creates and maintains the specialized categorization sets that extend this pattern (`*_not_futures`, `*_swap`, `*_delivery`).
* Various tasks and services across all projects read from these sets to retrieve trading pairs for specific markets, often filtered by trade type or currency.

**Related Keys:**

* `market_list:<trade_type>`: A Redis Set storing markets that support a specific trade type.
* `market_coin:<market_key>`: A Redis Set storing coins available on a specific market.
* `market_currency:<market_key>`: A Redis Sorted Set storing quote currencies available on a specific market.
* `market_stat_currency:<market_key>`: A Redis Set storing specialized quote currency categories available on a specific market.

**Examples (from code usage):**

* `market_trading:binance` - All trading pairs on Binance
* `market_trading:spot:okex` - All spot trading pairs on OKEx
* `market_trading:binance:usdt` - All USDT-quoted trading pairs on Binance
* `market_trading:okex:usdt_swap` - All USDT-quoted perpetual swap contracts on OKEx

---

## Key Prefix: `market_trading_currency`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure and Related Code):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `market_trading_currency` as a top-level prefix.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with this exact prefix.
* However, several related prefixes like `market_trading`, `market_currency`, and `trading_currency` (used in commented-out code) suggest that this prefix could be related to the relationships between markets, trading pairs, and quote currencies.
* The naming pattern suggests it might store mappings between markets, their trading pairs, and the currencies used for pricing these pairs.
* Based on similar patterns in the codebase, this prefix might be structured as:
  * `market_trading_currency:<market_key>` - Possibly mapping a market to its trading pairs and currencies
  * `market_trading_currency:<trade_type>:<market_key>` - Similar mapping but specific to a trade type
* The existence of this prefix alongside separate `market_trading` and `market_currency` prefixes suggests it might be:
  * Legacy keys that are no longer actively used (supported by the fact that related code like `trading_currency` appears in commented-out sections)
  * Used by specialized systems or scripts not included in the analyzed PHP projects
  * Generated for specific analytical purposes or reports

**Related Keys:**

* `market_trading:<market_key>:<currency_str>`: A Redis Set storing trading pairs for a specific market and quote currency.
* `market_currency:<market_key>`: A Redis Sorted Set storing quote currencies available on a specific market.
* `trading_currency:<coin_type>:<market_type>`: A pattern found in commented-out code that might have a similar function.

**Files Found (For Related Concepts):**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: Contains commented-out code referencing the similar `trading_currency` prefix.
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: Also contains commented-out references to `trading_currency`.

---

## Key Prefix: `oklink`

**Projects:** No direct usage found in the searched PHP projects (`back.aicoin.com`, `sosobtc_process`, `kline-data-scrapy`, `php-task`).

**Usage (Inferred from Key Structure and Context):**

* The `redis_top_level_prefixes_sorted.csv` file indicates `oklink` as a top-level prefix.
* No PHP code within the analyzed projects was found to directly interact with Redis keys starting with this exact prefix.
* OKLink is a blockchain explorer and data provider service, offering APIs for blockchain data, crypto market information, and on-chain analytics.
* Based on this context, the `oklink:` prefix is likely used to:
  * Cache data retrieved from the OKLink API to reduce API calls and improve performance
  * Store API authentication tokens or configuration settings for OKLink integration
  * Store processed or transformed data originally sourced from OKLink
* The key pattern could include:
  * `oklink:token` - Possibly storing an API access token
  * `oklink:cache:<endpoint>` - Caching responses from specific OKLink API endpoints
  * `oklink:data:<blockchain>:<address>` - Storing specific blockchain address data from OKLink

**Related External Services:**

* OKLink (https://www.oklink.com) - A blockchain explorer and data service provider
* The OKX ecosystem, which includes OKLink as a blockchain explorer service

**Files Found:**

* No PHP files within the searched projects were found to be directly interacting with this Redis key prefix.
* It's possible that the interaction with this prefix happens through:
  * Libraries or external packages not included in the main codebase
  * Scripts or services that run separately from the main application
  * Legacy code paths that are no longer active

---

## Key Prefix: `trading`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* `trading:` 前缀用于存储币种与交易市场之间的关系，这是一个关键的数据结构，帮助系统跟踪哪些币种在哪些交易所上可交易。
* 主要键模式如下：
  * `trading:<coin_type>:<market_type>` - Redis Set（集合），存储特定币种在特定市场上的所有交易对。这是币种和市场之间的关联。
  * `trading:<trade_type>:<coin_type>:<market_type>` - Redis Set，按交易类型（如现货、期货）存储特定币种在特定市场上的交易对（在代码中被注释掉，可能是旧版本的模式）。
  * `trading:<coin_type>:<market_type>:<currency_str>` - Redis Set，存储特定币种、市场和报价币种的交易对（在代码中被注释掉，可能是旧版本的模式）。
* 这些 Redis 集合用于：
  * 快速确定某个币种在特定交易所是否可交易
  * 获取特定币种在特定交易所的所有交易对
  * 在添加或删除交易对时维护交易所与币种之间的关系

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`：包含管理这些集合的核心逻辑，主要在 `marketCoinConf` 方法中：
  * 当添加新交易对时，使用 `$conf_redis->SADD('trading:' . $coin_type . ':' . $market_type, $trading_key)` 将交易对添加到相应的集合中。
  * 当删除交易对时，使用 `$conf_redis->SREM('trading:' . $coin_type . ':' . $market_type, $trading_key)` 从集合中移除交易对。
  * 检查集合是否为空，如果为空则更新相关的币种-市场关系：`if (!$conf_redis->SCARD('trading:' . $coin_type . ':' . $market_type)) { ... }`
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`：在批量操作中使用这些集合管理交易对和它们与币种、市场之间的关系。
* `/Users/<USER>/sosobtc_process/lib/tradingPairTrait.php`：与 back.aicoin.com 版本类似，在交易对创建或删除时管理这些集合。

**相关键：**

* `market_coin:<market_key>` - Redis Set，存储特定市场上可用的币种。
* `coin_market:<coin_key>` - Redis Set，存储特定币种可交易的市场。
* `market_trading:<market_key>` - Redis Set，存储特定市场上的所有交易对。
* `coin_trading:<coin_key>` - Redis Set，存储特定币种的所有交易对。

**示例（从代码使用中推断）：**

* `trading:btc:binance` - 币种 BTC 在 Binance 交易所上的所有交易对
* `trading:eth:okex` - 币种 ETH 在 OKEx 交易所上的所有交易对

---

## Key Prefix: `trading_currency`

**Projects:** 在已分析的PHP项目中没有找到直接使用，但在 `/Users/<USER>/back.aicoin.com` 的注释代码中有引用。

**Usage (从键名推断):**

* `trading_currency` 前缀可能用于存储与交易相关的货币信息，可能包括：
  * 交易对中使用的报价货币（如USDT、BTC等）
  * 交易所支持的货币种类
  * 货币的汇率信息
* 主要键模式可能包括：
  * `trading_currency:<trade_type>:<coin_type>:<market_type>` - Redis Sorted Set（有序集合），存储特定交易类型、币种和市场的报价币种。
  * `trading_currency:<coin_type>:<market_type>` - Redis Sorted Set，存储特定币种和市场的报价币种。
* 这个前缀似乎是用于记录特定币种在特定市场上可用的报价币种（如USDT、BTC等）。
* 从代码中被注释掉的情况来看，这个前缀可能已被替换为更新的数据结构，如 `market_currency` 和 `coin_currency` 前缀。

**相关代码示例（注释部分）:**

```php
// 在创建交易对时
//if (array_key_exists($currency_str, $currency_list) || !$conf_redis->zScore('trading_currency:'.$coin_type.':'.$market_type, $currency_str)) {
//    RedisClient::zadd("conf", 'trading_currency:'.$coin_type.':'.$market_type, $score, $currency_str);
//}

// 在删除交易对时
//if (!$conf_redis->SCARD('trading:'.$coin_type.':'.$market_type.':'.$currency_str)) {
//    $conf_redis->ZREM('trading_currency:'.$coin_type.':'.$market_type, $currency_str);
//}
```

**相关键：**

* `market_currency:<market_key>` - Redis Sorted Set，存储特定市场上可用的报价币种。
* `coin_currency:<coin_key>` - Redis Sorted Set，存储特定币种可用的报价币种。
* `trading:<coin_type>:<market_type>` - Redis Set，存储特定币种在特定市场上的所有交易对。

**总结：**

这个前缀很可能是一个遗留前缀，在系统重构过程中被其他更专门化的前缀（如 `market_currency` 和 `coin_currency`）所取代。这种变化可能是为了更有效地组织数据，将市场和币种的关系分开管理，而不是使用一个更复杂的组合前缀。

---

## Key Prefix: `user`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `user` 前缀用于存储用户相关的数据，包括用户账号信息、会话状态、权限控制和用户偏好设置等。
* 主要键模式如下：

  * `user_session:<user_id>` - Redis Hash，存储用户的会话信息，包括当前活跃会话ID等
  * `user:ews:close` - Redis Hash，存储用户的消息推送开关状态
  * `user_default_group:<user_id>` - 存储用户默认的自选分组
  * `user_custom_group:<user_id>` - Redis Sorted Set，存储用户的所有自选分组
  * `user_one_time_session:app:<user_id>:<session_id>` - 存储用户一次性会话令牌
  * `login_forbidden_<user_id>` - 标记用户是否被禁止登录
  * `login_fail_num_2fa_<user_id>` - 记录用户二次验证失败次数
  * `login_fail_num_password_<user_id>` - 记录用户密码验证失败次数
* 这些键大多数存储在专门的 `users` Redis数据库中，这表明系统对用户数据进行了逻辑分离。
* 用户ID在存储时通常会减去10000（`$userid - 10000`），这可能是为了内部ID规范化。

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Services/User/UserInfoService.php`: 包含用户信息管理的核心逻辑
* `/Users/<USER>/back.aicoin.com/app/Http/Services/User/UserClientService.php`: 管理用户客户端相关的数据
* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/User/UserInfoController.php`: 用户信息相关的控制器
* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/UserCustomController.php`: 管理用户自选内容的控制器
* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/EwsController.php`: 管理用户消息推送设置

**相关键：**

* `session:<session_id>` - Redis Hash，存储特定会话的详细信息
* `custom_group_key:<group_id>` - Redis Sorted Set，存储特定自选分组中的内容
* `custom_info:<group_id>` - Redis Hash，存储自选分组的元信息
* `trends_user_not`、`risk_device_id_black_list`、`risk_device_id_black_list_white_user` - 用于风险控制的用户黑白名单

**示例（从代码使用中推断）：**

* `user_session:12345` - ID为12345的用户的会话信息
* `login_forbidden_12345` - 标记ID为12345的用户是否被禁止登录
* `user_custom_group:12345` - ID为12345的用户的所有自选分组

---

## Key Prefix: `app`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* `app` 前缀是一个通用前缀，用于存储与应用程序相关的各种配置和数据。
* 主要键模式如下：
  * `app:<trading_pair_key>` - Redis Hash，存储特定交易对的缓存数据
  * `app_config` - Redis Hash，存储应用程序的全局配置
  * `app_adjust_time` - 存储最后一次调整的时间戳
  * `app_adjust_time_dev` - 开发环境的最后一次调整时间戳
  * `app_adjust_v1_list` - Redis Sorted Set，记录生产环境数据同步的变更
  * `app_adjust_v1_list_dev` - Redis Sorted Set，记录开发环境数据同步的变更
* 这个前缀作为一个顶级命名空间，包含了各种与前端应用相关的子前缀，例如 `app_tab_*`, `app_coin_*`, `app_market_*` 等。
* 这些键主要用于：
  * 缓存前端所需的数据以提高性能
  * 管理应用程序的配置项
  * 追踪环境之间的数据同步状态

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/AppController.php`: 应用程序控制器，处理应用程序级别的请求
* `/Users/<USER>/back.aicoin.com/app/Http/Services/AppService.php`: 应用程序服务，包含管理应用程序数据的业务逻辑
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/AppAdjustCommand.php`: 命令行工具，用于管理应用程序数据的调整和同步

**相关键：**

* `app_mix_tab_*` - 混合标签页相关的键
* `app_coin_tab_*` - 币种标签页相关的键
* `app_market_tab_*` - 市场标签页相关的键
* `app_futures_tab_*` - 期货标签页相关的键

**示例（从代码使用中推断）：**

* `app:btcusdt` - BTC/USDT 交易对的缓存数据
* `app_config` - 全局应用程序配置
* `app_adjust_time` - 最后一次调整的时间戳

---

## Key Prefix: `coin_trading`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* `coin_trading` 前缀用于存储特定币种的所有交易对信息，无论市场（交易所）是什么。
* 主要键模式如下：
  * `coin_trading:<coin_key>` - Redis Set，存储特定币种的所有交易对标识符。
  * `coin_trading:<trade_type>:<coin_key>` - Redis Set，存储特定币种在特定交易类型（如现货、期货）下的所有交易对。
* 这些 Redis 集合用于：
  * 快速找到特定币种在所有交易所的所有交易对
  * 过滤特定交易类型（现货/期货）的交易对
  * 在前端展示特定币种的市场概览

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: 包含管理这些集合的核心逻辑，主要在 `marketCoinConf` 方法中：
  * 当添加新交易对时，使用 `$conf_redis->SADD('coin_trading:' . $coin_type, $trading_key)` 将交易对添加到相应的集合中。
  * 当删除交易对时，使用 `$conf_redis->SREM('coin_trading:' . $coin_type, $trading_key)` 从集合中移除交易对。
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/TradingBatchOff.php`: 在批量操作中使用这些集合管理交易对。

**相关键：**

* `coin_trading_futures:<coin_key>` - Redis Set，存储特定币种的所有期货交易对。
* `coin_trading_spot:<coin_key>` - Redis Set，存储特定币种的所有现货交易对。
* `trading:<coin_type>:<market_type>` - Redis Set，存储特定币种在特定市场上的所有交易对。
* `coin_market:<coin_key>` - Redis Set，存储特定币种可交易的市场。

**示例（从代码使用中推断）：**

* `coin_trading:btc` - BTC 的所有交易对
* `coin_trading:spot:eth` - ETH 的所有现货交易对
* `coin_trading:futures:btc` - BTC 的所有期货交易对

---

## Key Prefix: `conf_market_list`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `conf_market_list` 前缀用于在配置 Redis 数据库中存储市场（交易所）列表相关的信息。
* 这个前缀很可能是作为 `market_list` 前缀的一个更结构化的扩展或替代，特别用于配置环境。
* 主要键模式可能包括：
  * `conf_market_list:all` - 可能存储所有可用市场的排序列表
  * `conf_market_list:online` - 可能存储当前在线的市场列表
  * `conf_market_list:<trade_type>` - 可能按交易类型（如现货、期货）存储市场列表

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Traits/MarketTrait.php`: 可能包含管理 `conf` Redis 数据库中市场列表的逻辑，特别是处理 `all_market_list`, `online_market_list`, `online_market_list_dev` 等键。

**相关键：**

* `all_market_list` - Redis Sorted Set，存储所有市场及其排名
* `online_market_list` - Redis Sorted Set，存储生产环境中在线的市场
* `online_market_list_dev` - Redis Sorted Set，存储开发环境中在线的市场
* `market_list` - Redis Set，存储所有可用的市场标识符

**示例（推断）：**

* `conf_market_list:all` - 所有市场的配置信息
* `conf_market_list:online` - 在线市场的配置信息

---

## Key Prefix: `config`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* `config` 前缀用于存储应用程序的各种配置设置。
* 这个前缀不同于 `app_config`，它可能更侧重于系统级配置而非应用级配置。
* 主要键模式可能包括：
  * `config:<module_name>` - Redis Hash，存储特定模块的配置
  * `config:<feature_name>` - Redis Hash，存储特定功能的配置
  * `config:<environment>:<setting_name>` - 存储特定环境的配置设置

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/config/`: 配置目录，可能包含与 Redis 配置交互的代码
* `/Users/<USER>/sosobtc_process/conf/`: 配置目录，可能包含与 Redis 配置交互的代码

**相关键：**

* `app_config` - 应用级配置
* 特定模块的配置键

**示例（推断）：**

* `config:api_rate_limits` - API 速率限制配置
* `config:notification_settings` - 通知设置配置
* `config:maintenance_mode` - 维护模式配置

---

## Key Prefix: `currency_info`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `currency_info` 前缀用于存储有关报价货币（如USDT、BTC等）的详细信息。
* 主要键模式如下：

  * `currency_info:<currency_key>` - Redis Hash，存储特定报价货币的详细信息。
* 这些 Redis Hash 可能包含的字段：

  * `name` - 货币的全名
  * `display_name` - 用于显示的名称
  * `icon` - 货币图标的URL
  * `order` - 排序顺序
  * `created_at` - 创建时间
  * `updated_at` - 更新时间

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/CurrencyController.php`: 可能包含管理报价货币信息的控制器
* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: 可能在创建交易对时引用或更新货币信息

**相关键：**

* `market_currency:<market_key>` - Redis Sorted Set，存储特定市场上可用的报价币种
* `coin_currency:<coin_key>` - Redis Sorted Set，存储特定币种可用的报价币种

**示例（推断）：**

* `currency_info:usdt` - USDT的详细信息
* `currency_info:btc` - BTC作为报价货币的详细信息

---

## Key Prefix: `trading_newly`

**Projects:** `/Users/<USER>/back.aicoin.com`, `/Users/<USER>/sosobtc_process`

**Usage:**

* `trading_newly` 前缀可能用于跟踪新上线的交易对。
* 主要键模式可能包括：

  * `trading_newly:<time_window>` - Redis Sorted Set，按时间排序的新交易对
  * `trading_newly:<market_key>` - Redis Sorted Set，特定市场的新交易对
* 这些键的主要用途可能包括：

  * 在前端突出显示新上线的交易对
  * 为用户提供最新上线的币种信息
  * 追踪交易所的扩展活动

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Traits/TradingPairTrait.php`: 可能在创建新交易对时更新这些键
* `/Users/<USER>/sosobtc_process/task/`: 可能包含定期更新新交易对信息的任务

**相关键：**

* `trading:<coin_type>:<market_type>` - Redis Set，存储特定币种在特定市场上的所有交易对
* `market_trading:<market_key>` - Redis Set，存储特定市场上的所有交易对

**示例（推断）：**

* `trading_newly:24h` - 过去24小时内新上线的交易对
* `trading_newly:binance` - Binance交易所新上线的交易对

---

## Key Prefix: `app_coin_offline_markets` & `app_coin_offline_markets_dev`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* 这两个前缀用于存储已下线的币种市场关系，分别用于生产环境和开发环境。
* 主要键模式如下：
  * `app_coin_offline_markets` - Redis Hash，存储生产环境中已下线的币种市场关系
  * `app_coin_offline_markets_dev` - Redis Hash，存储开发环境中已下线的币种市场关系
* 这些键存储的数据可能包括：
  * 特定币种在哪些市场上已被下线
  * 下线时间
  * 下线原因

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/AppController.php`: 可能包含管理这些下线数据的控制器
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/`: 可能包含同步和管理下线市场数据的命令行工具

**相关键：**

* `coin_currency_offline_hash` - 存储已下线的币种和报价币种关系
* `coin_currency_offline_hash_dev` - 开发环境中已下线的币种和报价币种关系
* `app_coin_tab_coins_online` - 在线的币种标签页币种
* `app_coin_tab_coins_online_dev` - 开发环境中在线的币种标签页币种

---

## Key Prefix: `app_market_coins`, `app_market_tab` & `app_market_tab_coins`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* 这些前缀用于管理应用程序中市场标签页相关的数据，特别是用于前端显示。
* 主要键模式如下：
  * `app_market_coins` - Redis Hash 或 Set，存储与应用市场相关的币种
  * `app_market_tab` - Redis Hash，存储市场标签页的配置信息
  * `app_market_tab_coins` - Redis Hash 或 Set，存储市场标签页中显示的币种列表
* 这些键的主要用途：
  * 配置前端应用中市场页面的展示
  * 管理市场标签页中显示的币种
  * 可能参与实现"自定义市场视图"功能

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/AppController.php`: 可能包含管理这些市场标签页数据的控制器
* `/Users/<USER>/back.aicoin.com/app/Http/Services/AppService.php`: 可能包含处理市场标签页业务逻辑的服务

**相关键：**

* `app_coin_tab` - 币种标签页相关配置
* `app_coin_tab_coins` - 币种标签页中显示的币种
* `app_futures_tab` - 期货标签页相关配置
* `app_mix_tab` - 混合标签页相关配置

---

## Key Prefix: `app_mix_tab`, `app_mix_tab_coins`, `app_mix_tab_coins_online` & `app_mix_tab_coins_online_dev`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* 这些前缀用于管理应用程序中混合标签页相关的数据，混合标签页可能显示多种类型的交易对（如现货和期货）。
* 主要键模式如下：
  * `app_mix_tab` - Redis Hash，存储混合标签页的配置信息
  * `app_mix_tab_coins` - Redis Hash 或 Set，存储混合标签页中显示的所有币种
  * `app_mix_tab_coins_online` - Redis Hash 或 Set，存储生产环境中混合标签页在线的币种
  * `app_mix_tab_coins_online_dev` - Redis Hash 或 Set，存储开发环境中混合标签页在线的币种
* 这些键的主要用途：
  * 配置前端应用中混合视图的展示
  * 管理不同环境中混合标签页的在线状态
  * 可能用于实现"多类型交易对综合视图"功能

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/app/Http/Controllers/AppController.php`: 可能包含管理这些混合标签页数据的控制器
* `/Users/<USER>/back.aicoin.com/app/Http/Services/AppService.php`: 可能包含处理混合标签页业务逻辑的服务
* `/Users/<USER>/back.aicoin.com/app/Console/Commands/AppAdjustCommand.php`: 可能包含同步混合标签页在线状态的命令

**相关键：**

* `app_coin_tab_coins_online` - 币种标签页中在线的币种
* `app_coin_tab_coins_online_dev` - 开发环境中币种标签页在线的币种
* `app_market_tab` - 市场标签页相关配置
* `app_futures_tab` - 期货标签页相关配置

---

## Key Prefix: `core_lib`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `core_lib` 前缀可能用于存储系统核心库的相关信息，可能包括：
  * 核心库的版本信息
  * 核心库的配置参数
  * 核心库的状态数据
* 这个前缀可能与系统底层框架或核心服务有关，而非业务逻辑

**主要逻辑位置和文件：**

* 可能位于系统核心库代码中，而非当前分析的业务代码中

**相关键：**

* `config` - 系统配置
* `app` - 应用程序相关信息

---

## Key Prefix: `crypto`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `crypto` 前缀可能用于存储与加密相关的数据，可能包括：
  * 加密密钥
  * 签名信息
  * 加密算法配置
  * 与交易所API安全通信相关的数据

**主要逻辑位置和文件：**

* 可能位于处理安全通信或加密验证的代码中，不在当前分析的主要业务逻辑中

**相关键：**

* `config` - 系统配置
* `app` - 应用程序相关信息

---

## Key Prefix: `dex_chain_detail`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `dex_chain_detail` 前缀可能用于存储去中心化交易所(DEX)和区块链详情相关的数据，可能包括：
  * 特定区块链的详细信息
  * DEX支持的区块链列表
  * 链上交易的相关参数
  * 智能合约地址

**主要逻辑位置和文件：**

* 可能位于处理DEX数据或区块链集成的模块中，不在当前分析的主要代码中

**相关键：**

* `coin_chainids` - 币种与区块链ID的映射
* `platform_coin_mapping` - 平台与币种的映射关系

---

## Key Prefix: `kline_dbkey_to_lspr`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名和相关前缀推断):**

* `kline_dbkey_to_lspr` 前缀用于存储数据库键与LSPR (Long/Short Position Ratio，多空持仓比例)之间的映射。
* 这是一组双向映射关系中的一部分，另一部分是 `kline_lspr_to_dbkey`。
* 主要键模式可能是：
  * `kline_dbkey_to_lspr:<trading_pair_key>` - 存储特定交易对的数据库键到LSPR标识符的映射

**相关键：**

* `kline_lspr_to_dbkey` - 反向映射，从LSPR标识符到数据库键
* `kline_dbkey_to_elite` - 类似的映射关系
* `kline_dbkey_to_oiav` - 类似的映射关系
* `kline_dbkey_to_ttv` - 类似的映射关系

---

## Key Prefix: `order_info`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `order_info` 前缀可能用于存储交易订单的信息，可能包括：
  * 订单详情
  * 订单状态
  * 订单历史
* 主要键模式可能是：
  * `order_info:<order_id>` - Redis Hash，存储特定订单的详细信息
  * `order_info:<user_id>:<status>` - Redis List，存储特定用户特定状态的订单列表

**相关键：**

* `user` - 用户相关信息
* `trading` - 交易对相关信息

---

## Key Prefix: `period` & `period_test`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `period` 前缀可能用于存储K线周期相关的配置或数据，`period_test` 是其测试环境版本。
* 主要键模式可能包括：
  * `period:<timeframe>` - 存储特定时间周期的配置（如1分钟、5分钟、1小时等）
  * `period:<trading_pair>:<timeframe>` - 存储特定交易对特定时间周期的数据或状态

**相关键：**

* 与K线数据相关的其他前缀，如 `kline_*`

---

## Key Prefix: `platform_coin_mapping`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `platform_coin_mapping` 前缀可能用于存储不同平台（可能是区块链平台或交易所）与币种之间的映射关系。
* 主要键模式可能包括：
  * `platform_coin_mapping:<platform_id>` - Redis Hash，存储特定平台支持的币种映射
  * `platform_coin_mapping:<platform_id>:<coin_id>` - 存储特定平台上特定币种的映射信息

**相关键：**

* `coin_chainids` - 币种与区块链ID的映射
* `dex_chain_detail` - DEX和区块链详情

---

## Key Prefix: `stat`

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage (从键名推断):**

* `stat` 前缀可能用于存储各种统计数据，可能包括：
  * 系统性能统计
  * 用户活动统计
  * 市场数据统计
  * 交易量统计
* 主要键模式可能包括：
  * `stat:<metric>` - 存储特定指标的统计数据
  * `stat:<category>:<metric>` - 存储特定类别下特定指标的统计数据
  * `stat:<timeframe>:<metric>` - 存储特定时间范围内的统计数据

**相关键：**

* `market_stat_currency` - 市场报价币种统计

---

## Key Prefix: `test_` 相关前缀

**Projects:** 在已分析的PHP项目中没有找到直接使用

**Usage:**

* 以 `test_` 开头的前缀（如 `test_coin_currency`, `test_coin_list`, `test_market_trading` 等）很可能是生产环境前缀的测试版本。
* 这些前缀可能用于：
  * 在不影响生产数据的情况下测试新功能
  * 在开发环境中模拟生产数据
  * 用于A/B测试或特性验证
* 这些前缀的结构和用途与其对应的生产前缀（不带 `test_` 前缀）类似，但专门用于测试环境，以避免干扰生产数据。主要用于：

* 新功能测试
* 数据同步测试
* 系统集成测试
* 性能测试

**主要逻辑位置和文件：**

* `/Users/<USER>/back.aicoin.com/tests/` - 测试用例目录
* `/Users/<USER>/sosobtc_process/bin-test/` - 测试脚本目录

**示例用途：**

```
test_coin_list -> 测试新币种添加流程
test_market_trading -> 测试新交易对上线流程
test_coin_currency -> 测试新报价币种支持
```

这些测试键通常在测试完成后会被清理，以避免数据累积。系统通过判断环境变量来决定使用正式键还是测试键。

---

## Key Prefix: `trade_remote`

**Projects:** `/Users/<USER>/back.aicoin.com`

**Usage:**

* `trade_remote` 前缀用于存储远程交易相关的数据。
* 主要键模式如下：
  * `trade_remote:<market_key>:<trading_pair>` - Redis Hash，存储特定市场和交易对的远程交易数据
  * `trade_remote:status:<market_key>` - Redis Hash，存储特定市场的远程交易状态
* 这些数据可能用于：
  * 跨系统交易数据同步
  * 远程交易执行状态追踪
  * 交易确认和验证

**相关键：**



* `market_trading` - 市场交易对信息
* `trading` - 交易相关数据
