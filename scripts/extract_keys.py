import redis
import csv
import time

# --- 配置 ---
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
RDB_DB_INDEX = 0 # 通常RDB文件加载到 DB 0，根据实际情况调整
OUTPUT_CSV_FILE = 'redis_all_keys.csv'
# OUTPUT_FULL_DUMP_CSV_FILE = 'redis_full_data_dump.csv' # 如果需要导出值
SCAN_COUNT = 2000 # 每次SCAN操作获取的key数量，根据你的Redis规模调整

def get_redis_connection(host, port, db):
    """建立Redis连接"""
    try:
        r = redis.Redis(host=host, port=port, db=db, decode_responses=True)
        r.ping()
        print(f"成功连接到 Redis: {host}:{port}, DB: {db}")
        return r
    except redis.exceptions.ConnectionError as e:
        print(f"无法连接到 Redis: {e}")
        return None

def extract_all_keys(r_conn):
    """使用SCAN命令提取所有key"""
    all_keys = []
    cursor = '0'
    start_time = time.time()
    print("开始提取所有key...")
    while True:
        cursor, keys_batch = r_conn.scan(cursor=cursor, count=SCAN_COUNT)
        if keys_batch: # 确保keys_batch不为空列表
            all_keys.extend(keys_batch)
        if cursor == '0' or not cursor : # scan 在迭代结束时返回 '0' (字符串)
             break # 迭代完成
        print(f"已提取 {len(all_keys)} 个key... (cursor: {cursor})")
    end_time = time.time()
    print(f"提取完成！总共 {len(all_keys)} 个key，耗时: {end_time - start_time:.2f} 秒")
    return all_keys

def export_keys_to_csv(keys, filename):
    """将key列表导出到CSV文件"""
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Key'])  # 写入表头
        for key in keys:
            writer.writerow([key])
    print(f"所有key已成功导出到: {filename}")

# (可选) 如果你不仅需要key，还需要key的类型和值（部分）
def export_full_data_to_csv(r_conn, keys, filename):
    """导出key, type, 和 value (部分) 到CSV"""
    print(f"开始导出详细数据到 {filename}...")
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Key', 'Type', 'Value_Sample', 'TTL (seconds)']) # 表头
        count = 0
        total_keys = len(keys)
        for key in keys:
            try:
                key_type = r_conn.type(key)
                ttl = r_conn.ttl(key) # 获取TTL, -1表示无过期, -2表示key不存在 (理论上scan出来的应该存在)

                value_sample = ""
                if key_type == 'string':
                    value_sample = r_conn.get(key)
                    if value_sample and len(value_sample) > 200: # 截断过长的字符串
                         value_sample = value_sample[:200] + "..."
                elif key_type == 'hash':
                    # 获取部分hash成员作为示例，避免数据量过大
                    sample_data = r_conn.hscan(key, count=5)[1]
                    value_sample = str(sample_data)
                    if len(value_sample) > 200:
                        value_sample = value_sample[:200] + "..."
                elif key_type == 'list':
                    # 获取列表头尾部分元素
                    sample_data = r_conn.lrange(key, 0, 4)
                    value_sample = str(sample_data)
                    if len(value_sample) > 200:
                        value_sample = value_sample[:200] + "..."
                elif key_type == 'set':
                    sample_data = r_conn.srandmember(key, 5) # 随机获取集合中5个元素
                    value_sample = str(sample_data)
                elif key_type == 'zset':
                    sample_data = r_conn.zrange(key, 0, 4, withscores=True) # 获取前5个元素及其分数
                    value_sample = str(sample_data)
                    if len(value_sample) > 200:
                        value_sample = value_sample[:200] + "..."
                # Add other types if needed (stream, etc.)
                else:
                    value_sample = f"({key_type} data)"

                writer.writerow([key, key_type, value_sample, ttl])
                count += 1
                if count % 100 == 0:
                    print(f"已处理 {count}/{total_keys} 个key...")

            except Exception as e:
                print(f"处理key '{key}' 时出错: {e}")
                writer.writerow([key, 'Error', str(e), r_conn.ttl(key) if r_conn.exists(key) else -2])

    print(f"详细数据已成功导出到: {filename}")


if __name__ == "__main__":
    redis_conn = get_redis_connection(REDIS_HOST, REDIS_PORT, RDB_DB_INDEX)

    if redis_conn:
        all_redis_keys = extract_all_keys(redis_conn)

        if all_redis_keys:
            export_keys_to_csv(all_redis_keys, OUTPUT_CSV_FILE)

            # --- 如果需要导出包括类型和部分值的详细文档，取消以下注释 ---
            print("\n--- 开始导出包含类型和部分值的详细文档 ---")
            OUTPUT_FULL_DUMP_CSV_FILE = 'redis_full_data_dump.csv'
            export_full_data_to_csv(redis_conn, all_redis_keys, OUTPUT_FULL_DUMP_CSV_FILE)
            print(f"包含类型和部分值的详细文档已保存到: {OUTPUT_FULL_DUMP_CSV_FILE}")
            # --- ---

        # 清理 (如果使用的是Docker并且希望在脚本执行后停止并移除容器)
        # import os
        # container_name = "my-redis-instance"
        # print(f"\n正在停止并移除Docker容器: {container_name}...")
        # os.system(f"docker stop {container_name}")
        # os.system(f"docker rm {container_name}")
        # print("Docker容器已停止并移除。")

        redis_conn.close()  